<!DOCTYPE html>
<html lang="en">
<head>
	<link rel="stylesheet" href="https://pro.fontawesome.com/releases/v6.0.0-beta3/css/all.css">
	<script src=https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
	<script src=https://code.jquery.com/ui/1.13.1/jquery-ui.js></script>
	<script src=https://cdnjs.cloudflare.com/ajax/libs/jquery-contextmenu/2.7.1/jquery.contextMenu.min.js></script>
	<script src=https://cdn.jsdelivr.net/npm/jquery-wheelcolorpicker@3.0.9/jquery.wheelcolorpicker.min.js></script>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.2/animate.min.css">
	<script src=https://cdn.jsdelivr.net/npm/simple-notify@0.5.4/dist/simple-notify.min.js></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        @import 'animate.compat.css';

        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --dark-bg: rgba(17, 24, 39, 0.95);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #ffffff;
            --text-secondary: #d1d5db;
            --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-xl: 0 35px 60px -12px rgba(0, 0, 0, 0.35);
            --blur-bg: blur(16px);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            user-select: none;
            -webkit-user-drag: none;
            outline: 0;
            font-family: 'Tajawal', 'Inter', sans-serif;
            font-weight: 500;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background: transparent;
            overflow: hidden;
            font-size: 14px;
            line-height: 1.5;
        }
        /* Modern Watermark Design */
        #Hud .watermarkBox {
            display: none;
            flex-direction: column;
            align-items: center;
            margin-top: 8px;
            gap: 8px;
        }

        #Hud .watermark {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 200px;
            height: 40px;
            padding: 0 20px;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            text-align: center;
            left: 82%;
            background: linear-gradient(135deg,
                var(--primary-color) 0%,
                var(--secondary-color) 50%,
                var(--accent-color) 100%);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            backdrop-filter: var(--blur-bg);
            border: 1px solid var(--glass-border);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transition: var(--transition);
            overflow: hidden;
        }

        #Hud .watermark::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.6s ease;
        }

        #Hud .watermark:hover::before {
            left: 100%;
        }

        #Hud .watermark:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }
        #Hud .watermark font {
            font: 1.5em 'Tajawal', sans-serif;
            font-weight: 600;
        }

        /* Modern Time Display */
        #Hud .Time {
            position: relative;
            margin-top: 8px;
            padding: 12px 20px;
            background: var(--dark-bg);
            backdrop-filter: var(--blur-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transition: var(--transition);
            min-width: 120px;
        }

        #Hud .Time:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            background: rgba(17, 24, 39, 0.98);
        }

        /* Modern Online Count */
        #Hud .Online {
            position: relative;
            margin-top: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, var(--success-color), var(--info-color));
            backdrop-filter: var(--blur-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            transition: var(--transition);
            min-width: 140px;
        }

        #Hud .Online:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        #Hud .Online::before {
            content: '👥';
            margin-right: 8px;
            font-size: 16px;
        }
#Hud #cinematic {
    border-top: solid 17vh;
    border-bottom: solid 19vh;
    height: 68vh;
    display: none;
}

#Hud .PlayerInfo2 {
			position: relative;
			display: flex;
			height: 40px;
			width: 200px;
            border-radius: 16px;
			padding: 8px 16px;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(99, 102, 241, 0.6);
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
			font-weight: 600;
			text-align: center;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

		}
		.PlayerInfo2 i {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 24px;
			width: 24px;
			margin-right: 8px;
			color: rgba(99, 102, 241, 0.9);
			font-size: 18px;
			text-shadow: 0 0 8px rgba(99, 102, 241, 0.5);
		}

		.PlayerInfo2 span {
			display: flex;
			align-items: center;
			justify-content: center;
			flex: 1;
			font-size: 14px;
			font-weight: 600;
			color: rgba(255, 255, 255, 0.95);
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
		}

		.PlayerInfo2:hover {
			transform: translateY(-2px) scale(1.02);
			box-shadow:
				0 12px 40px rgba(0, 0, 0, 0.3),
				0 0 0 1px rgba(255, 255, 255, 0.2),
				inset 0 1px 0 rgba(255, 255, 255, 0.3);
			border-color: rgba(99, 102, 241, 0.8);
		}
    
		#Hud .ServerAvater {
			position: absolute;
			left: 91.3vw;
			top: 6.8vh;
			border-radius: 5%;
			width: 175px;
			height: 175px;
			z-index: 7;
			background: linear-gradient(to top, rgba(0, 0, 0, 0.2), rgba(163, 131, 44, 1));
		}
		#Hud .ServerAvater1 {
			position: absolute;
			border-radius: 70%;
			width: 120px;
			height: 120px;
			z-index: 7;
		}
		#Hud .ServerAvater1 img {
			display: flex;
			width: 95px;
			margin-top: 0px;
			margin-left: 5px;
			border-radius: 70%;
		}
		#Hud .ServerAvater img {
			display: flex;
			width: 110px;
			border-radius: 70%;
		}




		#Hud #Job {
			position: absolute;
			top: 13vh;	
			left:  87.6vw;
		}

		#Hud #wallet {
			position: absolute;
			top: 13vh;	
			left: 76.1vw;
		}

		#Hud #Bank {
			position: absolute;
            top: 10.5vh;
			left: 76.1vw;
		}

		#Hud #id {
			position: absolute;
			top: 10.5vh;
			left: 87.6vw;
		}
		#Hud #onl {
			position: absolute;
			top: 10.5vh;
			left: 87.6vw;
		}

		#Hud #ServerName {
			position: absolute;
			top: 15vh;
			left: 82.3vw;
		}
		#Hud .PlayerInfo5 {
            position: relative;
            display: flex;
            width: 180px;
            height: 40px;
            padding: 8px 16px;
            text-align: center;
            align-items: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(99, 102, 241, 0.6);
            border-radius: 16px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		}

        #Hud .PlayerInfo5 i {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 24px;
			width: 24px;
			margin-right: 8px;
			color: rgba(99, 102, 241, 0.9);
			font-size: 18px;
			text-shadow: 0 0 8px rgba(99, 102, 241, 0.5);
        }

		#Hud .PlayerInfo5 span {
			display: flex;
			align-items: center;
			justify-content: center;
			flex: 1;
			font-size: 14px;
			font-weight: 600;
			color: rgba(255, 255, 255, 0.95);
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
		}

		.PlayerInfo5:hover {
			transform: translateY(-2px) scale(1.02);
			box-shadow:
				0 12px 40px rgba(0, 0, 0, 0.3),
				0 0 0 1px rgba(255, 255, 255, 0.2),
				inset 0 1px 0 rgba(255, 255, 255, 0.3);
			border-color: rgba(99, 102, 241, 0.8);
		}

		#Hud .PlayerInfo {
            position: relative;
            display: flex;
            width: 180px;
            height: 40px;
            padding: 8px 16px;
            text-align: center;
            align-items: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
			background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(99, 102, 241, 0.6);
            border-radius: 16px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

		}

        #Hud .PlayerInfo i {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 24px;
			width: 24px;
			margin-right: 8px;
			color: rgba(99, 102, 241, 0.9);
			font-size: 18px;
			text-shadow: 0 0 8px rgba(99, 102, 241, 0.5);
        }

		#Hud .PlayerInfo span {
			display: flex;
			align-items: center;
			justify-content: center;
			flex: 1;
			font-size: 14px;
			font-weight: 600;
			color: rgba(255, 255, 255, 0.95);
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
		}

		.PlayerInfo:hover {
			transform: translateY(-2px) scale(1.02);
			box-shadow:
				0 12px 40px rgba(0, 0, 0, 0.3),
				0 0 0 1px rgba(255, 255, 255, 0.2),
				inset 0 1px 0 rgba(255, 255, 255, 0.3);
			border-color: rgba(99, 102, 241, 0.8);
		}
        #Hud .PlayerInfo3 {
            position: relative;
            display: flex;
            width: 115px;
            padding: 0.05vh 2vh;
            text-align: center;
            align-items: center;
            text-shadow: 1px 1px 2px black;
			background: linear-gradient(to right, rgba(0, 0, 0, 0.1), rgba(163, 131, 44, 1));
            box-shadow: rgb(0 0 0 / 25%) 0 .0625em 3px,rgb(0 0 0 / 55%) 0 .025em .5em,rgb(255 255 255 / 10%) 0 0 0 1px inset;

		}

        #Hud .PlayerInfo3 i {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 20px;
			width: 10px;
			margin-right: 10px;
			color: #ffffff;
			border-radius: 60px;
			padding: 0px 10px;
			position: relative;
			top: 3px;
			right: 20.5px;
        }

		#Hud .PlayerInfo3 span {
			display: flex;
			align-items: center;
			justify-content: space-around;
			width: auto !important;
			min-width: 150px;
			font-size: 13.5px;
			font-weight: 600;
			color: #fff;
			text-shadow: 0px 0px 6px rgb(0 0 0 / 63%), 0 8px 13px rgb(0 0 0 / 10%), 0 18px 23px rgb(0 0 0 / 10%);
			position: relative;
			right: 45.5px;
		}
		#Hud .PlayerInfo1 {
            position: relative;
            display: flex;
            width: 115px;
            padding: 0.05vh 2vh;
            text-align: center;
            align-items: center;
            text-shadow: 1px 1px 2px black;
			background: linear-gradient(to right, rgba(0, 0, 0, 0.1), rgba(163, 131, 44, 1));
            box-shadow: rgb(0 0 0 / 25%) 0 .0625em 3px,rgb(0 0 0 / 55%) 0 .025em .5em,rgb(255 255 255 / 10%) 0 0 0 1px inset;

		}

        #Hud .PlayerInfo1 i {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 30px;
			width: 10px;
			margin-right: 10px;
			color: #ffffff;
			border-radius: 60px;
			padding: 0px 10px;
			position: relative;
			top: 0px;
			left: 5.5vw;
        }

		#Hud .PlayerInfo1 span {
			display: flex;
			align-items: center;
			justify-content: space-around;
			width: auto !important;
			min-width: 150px;
			font-size: 13.5px;
			font-weight: 600;
			color: #fff;
			text-shadow: 0px 0px 6px rgb(0 0 0 / 63%), 0 8px 13px rgb(0 0 0 / 10%), 0 18px 23px rgb(0 0 0 / 10%);
			position: relative;
			right: 60.5px;
		}


#Hud .Weapon {
    display: none;
    position: absolute;
    left: 165vh;
    top: 50vw;
}
#Hud .Weapon .img img {
    width: 110px;
    height: 40px;
}
#Hud .Weapon .ammo {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 5px;
}
#Hud .Weapon .ammo img {
    width: 20px;
    height: 25px;
    margin-right: 7px;
}
#Hud .Weapon .ammo span {
    color: #fff;
    font-weight: 500;
}
#Hud .HotKays {
    position: absolute;
    left: 0.3vw;
    top: 40vh;
}
#Hud .HotKays .HotKay {
    display: flex;
    align-items: center;
    margin-top: 10px;
}
#Hud .HotKays .HotKay .Kay {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
    border: solid 2px #ffffffb4;
    border-radius: 10px;
    width: 30px;
    height: 30px;
}
#Hud .HotKays .HotKay span {
    color: #fff;
    font-family: Tajawal,sans-serif
}
#Hud .outline {
    animation: slide-in-elliptic-right-fwd .7s cubic-bezier(.25,.46,.45,.94) both;
    border: none;
    border-radius: 15px;
    border-left: 15px;
    border-right: 15px;
    position: fixed;
    bottom: 8vh;
    left: 5.8vh;
    width: 29.629vh;
    text-align: center;
    z-index: -1;
}
        /* Modern Map Design */
        #Hud .outline .mapborder {
            width: 28vh;
            height: 26vh;
            border: 3px solid var(--accent-color);
            position: relative;
            border-radius: 50%;
            background: transparent !important;
            backdrop-filter: none;
            box-shadow: var(--shadow-xl);
            transition: var(--transition);
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #Hud .outline .mapborder::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                var(--primary-color),
                var(--secondary-color),
                var(--accent-color),
                var(--primary-color));
            border-radius: inherit;
            z-index: -1;
            animation: borderGlow 3s linear infinite;
        }

        @keyframes borderGlow {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        #Hud .outline .mapborder:hover {
            transform: scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .square {
            border-radius: var(--border-radius) !important;
        }

        .square::before {
            border-radius: var(--border-radius) !important;
        }

        #Hud .outline .mapborder .circle {
            border-radius: var(--border-radius);
        }

        /* إصلاح محتوى الخريطة */
        #Hud .outline .mapborder > * {
            width: 100% !important;
            height: 100% !important;
            border-radius: inherit !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
        }

        /* إصلاح الخريطة المربعة */
        #Hud .outline .mapborder.square {
            border-radius: 15px !important;
        }

        #Hud .outline .mapborder.square > * {
            border-radius: 15px !important;
        }

        /* إصلاح الخريطة الدائرية */
        #Hud .outline .mapborder.circle {
            border-radius: 50% !important;
        }

        #Hud .outline .mapborder.circle > * {
            border-radius: 50% !important;
        }
        /* Modern Postals Container */
        #Hud .postals-container {
            position: absolute;
            height: 40px;
            width: 200px;
            background: var(--dark-bg);
            backdrop-filter: var(--blur-bg);
            border: 2px solid var(--accent-color);
            color: var(--text-primary);
            text-align: center;
            border-radius: var(--border-radius);
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: var(--shadow-lg);
            bottom: 345px;
            left: 120px;
            transition: var(--transition);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            font-weight: 600;
        }

        #Hud .postals-container:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        #Hud .postals-container::before {
            content: '📍';
            margin-right: 8px;
            font-size: 16px;
        }

        #Hud .postals-container:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        #Hud .postals-container::before {
            content: '📍';
            margin-right: 8px;
            font-size: 16px;
        }
#Hud .postals-container #nearest-postal,#Hud .postals-container .nearest-postal {
    position: absolute;
    margin-left:-7%;
	margin-top: -25px;
    font-size: 18px;
    font-weight: bold;
}
#Hud .postals-container .nearest-postal {
    color: rgba(255,255,255,.9);
}
#Hud #nearest-postal {
    position: absolute;
    margin-left:-8%;
	top: 92%;
    color: rgba(255,255,255,.9);
    text-align: center
}
#Hud #postals img {
	width: 18px;
	position: absolute;
	left: 4px;
	top: 50%;
	transform: translateY(-50%)
}
#Hud #health {
    position: absolute;
    top: 92vh;
    left: 2vw;
}
#Hud #armor {
    position: absolute;
    top: 92vh;
    left: 5vw;
}
#Hud #hunger {
    position: absolute;
    top: 92vh;
    left: 8vw;
}
#Hud #hunger1 {
    position: absolute;
    top: 9vh;
    left: 8vw;
}
#Hud #thirst {
    position: absolute;
    top: 92vh;
    left: 11vw;
}
#Hud #stamina {
    position: absolute;
    top: 92vh;
    left: 14vw;
}

#Hud .progressplayer {
    transition: stroke-dashoffset .35s ease 0s;
    transform: rotate(270deg);
    transform-origin: 50% 50%;
    stroke-linecap: round;
    border-radius: 100%;
    border: 7px solid #fff
}
#Hud .progressplayer image {
    fill: #f5f5f5;
}
        /* Modern Vehicle Stats */
        .carStats {
            display: none;
            position: relative;
        }

        .carStats .stat {
            background: linear-gradient(135deg, var(--dark-bg), rgba(17, 24, 39, 0.8));
            backdrop-filter: var(--blur-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            text-align: center;
            padding: 12px;
            margin: 8px;
            transition: var(--transition);
        }

        .carStats .stat:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .carStats .stat .icon {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .fa-icon {
            position: absolute;
            left: 5%;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .carStats .stat span {
            white-space: nowrap;
            color: var(--text-primary);
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
#gas img {
    width: 40%;
    margin-left: -1px
}
#damage img {
    width: 40%;
    margin-left: 0
}
#air img {
    margin-left: -2px
}
.progress {
    -webkit-transition: .35s stroke-dashoffset;
    -o-transition: .35s stroke-dashoffset;
    transition: .35s stroke-dashoffset;
    -webkit-transform: rotate(140deg);
    -ms-transform: rotate(140deg);
    transform: rotate(140deg);
    -webkit-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
    transform-origin: 50% 50%
}
.backgroundCircle,.outer {
    -webkit-transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    transform: rotate(-180deg);
    -webkit-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
    transform-origin: 50% 50%
}
.dials {
    -webkit-transform: scale(.8);
    -ms-transform: scale(.8);
    transform: scale(.8);
    text-align: center;
    background-repeat: no-repeat;
    background-position: 50% 40%;
    background-size: 45% 45% d
}
.dials span {
    color: rgba(255,255,255,.8);
    font-size: 12px
}
.dials svg {
    opacity: 1 !important
}
#idnumber {
    fill: #f5f5f5;
    overflow: hidden;
    font: 14px Arial,Helvetica,sans-serif;
}
.dials span {
    opacity: 0
}
.dials span:after {
    position: relative;
    content: "%";
    font-size: 7px
}
.dials g {
    -webkit-transform: translate(16px,13px);
    -ms-transform: translate(16px,13px);
    transform: translate(16px,13px)
}
.counter {
    position: absolute;
    left: 21.8vw;
    top: 84vh;
}
.counter .counter2 {
    height: 200%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%)
}
.counter .counter2 div {
    display: inline-block;
    vertical-align: middle;
    margin: 0 10px
}
.counter .counter2 div svg {
    opacity: .2
}
.counter .counter2 .active svg {
    opacity: .8
}
        /* Modern Speedometer */
        .counter .counter2 .speedometer {
            width: 120px;
            height: 120px;
            text-align: center;
            background: var(--dark-bg);
            backdrop-filter: var(--blur-bg);
            border: 1px solid var(--glass-border);
            border-radius: 50%;
            box-shadow: var(--shadow-xl);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .counter .counter2 .speedometer:hover {
            transform: scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .counter .counter2 .speedometer::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(
                from 0deg,
                transparent,
                rgba(99, 102, 241, 0.1),
                transparent
            );
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            to {
                transform: rotate(360deg);
            }
        }

        .counter .counter2 .speedometer svg {
            opacity: 1;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: scale(2.2) translate(-25%, -25%);
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
            z-index: 2;
        }

        .counter .counter2 .speedometer .outer {
            opacity: 0.4;
            transform: rotate(180deg) scale(1.2);
        }

        .counter .counter2 .speedometer .text {
            width: 100%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            margin: 0;
            z-index: 3;
        }

        .counter .counter2 .speedometer .text .speed {
            color: var(--text-primary);
            display: block;
            font-size: 24px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            margin-bottom: 4px;
        }

        .counter .counter2 .speedometer .text .mph {
            color: var(--text-secondary);
            display: block;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
#fuel {
    position: absolute;
    left: 22.5vw;
    top: 89vh;
}
#seatbelt {
    position: absolute;
    left: 24.5vw;
    top: 89vh;
}
#lights {
    position: absolute;
    left: 26.5vw;
    top: 89vh;
}
        /* Modern Control Panel */
        .Panl {
            display: block;
            width: 95vw;
            max-width: 1200px;
            height: 90vh;
            max-height: 800px;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            backdrop-filter: blur(25px);
            border: 2px solid rgba(99, 102, 241, 0.6);
            border-radius: 20px;
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 999;
            overflow: hidden;
            animation: panelSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            /* منع الحركة */
            pointer-events: auto;
        }

        @keyframes panelSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        /* Modern Header */
        .header {
            background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.9) 0%,
                rgba(139, 92, 246, 0.9) 50%,
                rgba(99, 102, 241, 0.9) 100%);
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 18px 18px 0 0;
            height: 90px;
            padding: 0 32px;
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        /* Header Sections */
        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .header-center {
            display: flex;
            justify-content: center;
            flex: 1;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1;
            justify-content: flex-end;
        }

        /* Version Badge */
        .version-badge {
            background: rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            backdrop-filter: blur(8px);
        }

        /* Quick Actions */
        .quick-actions {
            display: flex;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 12px;
            backdrop-filter: blur(8px);
        }

        .quick-btn {
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 16px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quick-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .quick-btn:active {
            transform: scale(0.95);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.1),
                transparent);
            transition: left 0.6s ease;
        }

        .header:hover::before {
            left: 100%;
        }

        /* Panel Content Area */
        .Panl .content {
            padding: 32px;
            height: calc(100% - 90px);
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color) transparent;
            background: transparent;
        }

        .Panl .content::-webkit-scrollbar {
            width: 6px;
        }

        .Panl .content::-webkit-scrollbar-track {
            background: transparent;
        }

        .Panl .content::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .Panl .content::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        /* Panel Sections */
        .panel-section {
            margin-bottom: 32px;
            padding: 24px;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.08) 100%);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 16px;
            backdrop-filter: blur(12px);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .panel-section h3 {
            margin: 0 0 16px 0;
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .panel-section p {
            margin: 0 0 12px 0;
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
        }

        /* Modern Health & Status Circles */
        .circle {
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
            transition: var(--transition);
        }

        .circle:hover {
            filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.3));
            transform: scale(1.05);
        }

        .progressplayer {
            transition: var(--transition);
        }

        /* Health Circle */
        .progressplayer.health {
            filter: drop-shadow(0 0 8px rgba(204, 60, 46, 0.4));
        }

        /* Armor Circle */
        .progressplayer.armor {
            filter: drop-shadow(0 0 8px rgba(0, 142, 255, 0.4));
        }

        /* Hunger Circle */
        .progressplayer.hunger {
            filter: drop-shadow(0 0 8px rgba(255, 201, 0, 0.4));
        }

        /* Thirst Circle */
        .progressplayer.thirst {
            filter: drop-shadow(0 0 8px rgba(0, 230, 255, 0.4));
        }

        /* Stamina Circle */
        .progressplayer.stamina {
            filter: drop-shadow(0 0 8px rgba(237, 255, 0, 0.4));
        }

        /* Vehicle Status Circles */
        .progressplayer.fuel {
            filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.3));
        }

        .progressplayer.seatbelt {
            filter: drop-shadow(0 0 6px rgba(255, 0, 0, 0.4));
        }

        .progressplayer.lights {
            filter: drop-shadow(0 0 6px rgba(255, 0, 0, 0.4));
        }

        /* Modern HUD Container */
        #Hud {
            position: relative;
            z-index: 100;
        }

        /* Animated Background Elements */
        .hud-bg-effect {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
        }

        .hud-bg-effect::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, var(--primary-color) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, var(--secondary-color) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, var(--accent-color) 0%, transparent 50%);
            animation: bgPulse 8s ease-in-out infinite;
        }

        @keyframes bgPulse {
            0%, 100% { opacity: 0.05; }
            50% { opacity: 0.15; }
        }

        /* Glowing Effects for Active Elements */
        .glow-effect {
            position: relative;
        }

        .glow-effect::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border-radius: inherit;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .glow-effect:hover::after {
            opacity: 0.3;
        }
.header1 {
    border-bottom: 1px solid gray;
    display: flex;
    justify-content: space-between;
    border-radius: 10px 10px 0 0;
    height: 30px;
    position: relative;
    width: 600px;
    bottom: 10px;
    left: -3.1%;
}
.header .options {
    display: block;

}
.header .options1 {
    display: block;

}
.options-button1 {
    display: block;
    position: relative;
    left: 8.7vw;
    top: 280px;
    color: #fff;
    width: 170px;
    gap: 20px; /* يمكنك تعديل هذه القيمة حسب الحاجة */
    border-radius: 5px;
    z-index: 99;
    height: 30px;
    margin: 15px 10px; /* تعديل المسافة بين الأزرار */
	border-radius: 15px;
	background: rgba(130, 130, 130, .75);
	box-shadow: rgba(0, 0, 0, .25) 0 0 4px 0;
    margin-top: 5px;
    margin-bottom: 5px;
	font-family: Tajawal, sans-serif;
	font-weight: 700;
    font-size: 16px;

	color: #fff;
    text-shadow: rgb(0, 0, 0) 0 3px 5px;
	line-height: 21px;
    text-align: center;
    margin: 15px 10px; /* تعديل المسافة بين الأزرار */

}

.options-button1 img {
    vertical-align: middle; /* لضبط صورة الأيقونة مع النص */
    margin-right: 5px; /* مسافة بين الأيقونة والنص */
    
}
.header .options1 .options-button1 span {
    margin-top: 5px;
    margin-bottom: 5px;
	font-family: Tajawal, sans-serif;
	font-weight: 700;
	color: #fff;
    text-shadow: rgb(0, 0, 0) 0 3px 5px;

	line-height: 21px
}
#color-position1 {
    border-top: solid 1px #fff;
    border-bottom: solid 1px #fff;
}
.header .options1 .options-button1 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 888;
}
        /* Modern Options Buttons */
        .header .options {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .options-button {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px 16px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: var(--text-primary);
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            min-width: 120px;
            height: 40px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .options-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.4s ease;
        }

        .options-button:hover::before {
            left: 100%;
        }

        .options-button:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .options-button:active {
            transform: translateY(0);
        }

        /* Modern Settings Buttons */
        .options-button img {
            vertical-align: middle;
            margin-right: 8px;
            width: 18px;
            height: 18px;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        }

        #color-position {
            border-top: 1px solid var(--glass-border);
            border-bottom: 1px solid var(--glass-border);
        }

        .header .options .options-button {
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            gap: 8px;
        }

        /* Specific Button Styles */
        #button-Share {
            background: linear-gradient(135deg, var(--info-color), var(--primary-color));
        }

        #button-Share:hover {
            background: linear-gradient(135deg, #2563eb, var(--primary-color));
            transform: translateY(-2px);
        }

        #button-color {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
        }

        #button-color:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            transform: translateY(-2px);
        }

        #button-position {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
        }

        #button-position:hover {
            background: linear-gradient(135deg, #d97706, #b45309);
            transform: translateY(-2px);
        }

        .header .options .options-button img {
            width: 18px;
            margin-right: 8px;
        }

        /* Modern Style Options */
        .style-options {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .style-option {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .style-option label {
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .modern-select {
            background: var(--dark-bg);
            backdrop-filter: var(--blur-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 500;
            padding: 12px 16px;
            transition: var(--transition);
            cursor: pointer;
        }

        .modern-select:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .modern-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        .modern-select option {
            background: var(--dark-bg);
            color: var(--text-primary);
            padding: 8px 12px;
        }

        .modern-button {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: var(--border-radius);
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
            padding: 12px 20px;
            cursor: pointer;
            transition: var(--transition);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .modern-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .modern-button:active {
            transform: translateY(0);
        }

        /* Toggle Grid Layout */
        .toggle-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .toggle-label {
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            margin-bottom: 4px;
        }

        /* Tab System */
        .tab-container {
            width: 100%;
        }

        .tab-nav {
            display: flex;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 6px;
            margin-bottom: 32px;
            gap: 6px;
            backdrop-filter: blur(8px);
        }

        .tab-btn {
            flex: 1;
            padding: 14px 20px;
            background: transparent;
            border: none;
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .tab-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
        }

        .tab-btn.active {
            background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.9) 0%,
                rgba(139, 92, 246, 0.9) 100%);
            color: white;
            box-shadow:
                0 4px 16px rgba(99, 102, 241, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .tab-btn.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                transparent 50%,
                rgba(255, 255, 255, 0.1) 100%);
            pointer-events: none;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Style Grid */
        .style-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .style-card {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.08) 100%);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(8px);
        }

        .style-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .style-preview {
            height: 80px;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(4px);
        }

        .preview-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid var(--danger-color);
            background: rgba(239, 68, 68, 0.1);
        }

        .preview-info {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 16px;
        }

        .preview-map {
            width: 40px;
            height: 40px;
            border: 2px solid var(--accent-color);
            border-radius: 50%;
            background: rgba(245, 158, 11, 0.1);
        }

        .map-controls {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .map-options {
            display: flex;
            gap: 12px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .radio-option input[type="radio"] {
            accent-color: var(--primary-color);
        }

        .radio-option:hover {
            color: var(--text-primary);
        }

        /* Elements Grid */
        .elements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }

        .element-category {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.08) 100%);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 24px;
            backdrop-filter: blur(8px);
        }

        .element-category h4 {
            margin: 0 0 16px 0;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .element-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .element-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.06) 0%,
                rgba(255, 255, 255, 0.03) 50%,
                rgba(255, 255, 255, 0.06) 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(4px);
        }

        .element-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
        }

        .element-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .element-icon {
            font-size: 22px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg,
                rgba(99, 102, 241, 0.2) 0%,
                rgba(139, 92, 246, 0.2) 100%);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 10px;
            backdrop-filter: blur(4px);
        }

        .element-details {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .element-name {
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
        }

        .element-desc {
            color: var(--text-secondary);
            font-size: 12px;
        }

        /* Modern Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 26px;
        }

        .toggle-switch input[type="checkbox"] {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-switch label {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(107, 114, 128, 0.5);
            border-radius: 13px;
            transition: var(--transition);
        }

        .toggle-switch label:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 3px;
            bottom: 3px;
            background: white;
            border-radius: 50%;
            transition: var(--transition);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch input:checked + label {
            background: linear-gradient(135deg, var(--success-color), var(--info-color));
        }

        .toggle-switch input:checked + label:before {
            transform: translateX(24px);
        }

        .toggle-switch:hover label {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        /* Color Categories */
        .color-categories {
            display: flex;
            flex-direction: column;
            gap: 32px;
            margin-top: 24px;
        }

        .color-category {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 24px;
        }

        .color-category h4 {
            margin: 0 0 20px 0;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .color-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .color-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .color-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
        }

        .color-preview {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.2);
            border: 2px solid var(--glass-border);
        }

        .preview-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid;
        }

        .health-color-preview { border-color: #cc3c2e; background: rgba(204, 60, 46, 0.1); }
        .armor-color-preview { border-color: #3585da; background: rgba(53, 133, 218, 0.1); }
        .hunger-color-preview { border-color: #86fa01; background: rgba(134, 250, 1, 0.1); }
        .thirst-color-preview { border-color: #00b6e3; background: rgba(0, 182, 227, 0.1); }
        .stamina-color-preview { border-color: #ffc100; background: rgba(255, 193, 0, 0.1); }

        .preview-text {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 14px;
        }

        .money-color-preview { color: #10b981; }
        .bank-color-preview { color: #3b82f6; }

        .color-controls {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .color-controls label {
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
        }

        .color-input-group {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .color-input-group input[type="color"] {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            background: none;
        }

        .color-input-group input[type="text"] {
            flex: 1;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            color: var(--text-primary);
            font-size: 14px;
        }

        .color-reset-btn {
            width: 32px;
            height: 32px;
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: var(--border-radius);
            color: var(--text-primary);
            cursor: pointer;
            transition: var(--transition);
            font-size: 12px;
        }

        .color-reset-btn:hover {
            background: rgba(239, 68, 68, 0.3);
            transform: scale(1.1);
        }

        /* Advanced Color Tools */
        .advanced-color-tools {
            margin-top: 32px;
            padding: 24px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
        }

        .advanced-color-tools h4 {
            margin: 0 0 20px 0;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
        }

        .color-tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .color-tool {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .color-tool label {
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
        }

        .tool-controls {
            display: flex;
            gap: 8px;
        }

        .tool-controls select,
        .tool-controls input {
            flex: 1;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            color: var(--text-primary);
            font-size: 14px;
        }

        .tool-btn {
            padding: 8px 16px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: var(--border-radius);
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            white-space: nowrap;
        }

        .tool-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Advanced Settings */
        .advanced-settings {
            display: flex;
            flex-direction: column;
            gap: 32px;
            margin-top: 24px;
        }

        .settings-category {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 24px;
        }

        .settings-category h4 {
            margin: 0 0 20px 0;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .settings-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .setting-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-color);
        }

        .setting-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .setting-name {
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
        }

        .setting-desc {
            color: var(--text-secondary);
            font-size: 12px;
            line-height: 1.4;
        }

        /* Range Control */
        .range-control {
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 200px;
        }

        .range-control input[type="range"] {
            flex: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .range-control input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .range-control input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .range-value {
            color: var(--text-primary);
            font-size: 12px;
            font-weight: 600;
            min-width: 50px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
        }

        /* Presets */
        .presets-container {
            display: flex;
            flex-direction: column;
            gap: 32px;
            margin-top: 24px;
        }

        .presets-category {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 24px;
        }

        .presets-category h4 {
            margin: 0 0 20px 0;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .presets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .preset-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 20px;
            transition: var(--transition);
            cursor: pointer;
        }

        .preset-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        .preset-preview {
            height: 80px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: var(--border-radius);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .preview-elements {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .preview-elements > div {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
        }

        .preset-info h5 {
            margin: 0 0 8px 0;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
        }

        .preset-info p {
            margin: 0 0 16px 0;
            color: var(--text-secondary);
            font-size: 12px;
            line-height: 1.4;
        }

        .preset-actions {
            display: flex;
            gap: 8px;
        }

        .preset-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }

        .apply-btn {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
        }

        .preview-btn {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid var(--glass-border);
        }

        .create-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .preset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* Custom Presets */
        .custom-presets {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .custom-preset-creator {
            background: rgba(255, 255, 255, 0.05);
            border: 2px dashed var(--glass-border);
            border-radius: var(--border-radius);
            padding: 24px;
            text-align: center;
        }

        .custom-preset-creator h5 {
            margin: 0 0 16px 0;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 600;
        }

        .creator-form {
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: 400px;
            margin: 0 auto;
        }

        .creator-form input,
        .creator-form textarea {
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            color: var(--text-primary);
            font-size: 14px;
        }

        .creator-form textarea {
            resize: vertical;
            min-height: 80px;
        }

        .saved-presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
        }
.header .options .options-button span {
    margin-top: 5px;
    margin-bottom: 5px;
	font-family: Tajawal, sans-serif;
	font-weight: 700;
	color: #fff;
    text-shadow: rgb(0, 0, 0) 0 3px 5px;

	line-height: 21px
}
        /* Modern Close Buttons */
        #close, #close1 {
            width: 40px;
            height: 40px;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(8px);
        }

        #close:hover, #close1:hover {
            background: rgba(239, 68, 68, 0.2);
            border-color: rgba(239, 68, 68, 0.5);
            transform: scale(1.05);
        }

        #close:active, #close1:active {
            transform: scale(0.95);
        }

        .custom-options {
            position: relative;
            cursor: pointer;
            filter: invert(100%);
        }
.white-icon {
    filter: invert(100%)
}
.seatbelt-img {
    filter: invert(25%) sepia() saturate(10000%) hue-rotate(0deg)
}
        /* Modern Title */
        #title-hud {
            color: var(--text-primary);
            font-size: 20px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        #title-hud::before {
            content: '⚙️';
            font-size: 24px;
        }
.row-content {
    position: absolute;
    left:1vw;
    top: 40px;
}
.row {
    display: table-row;

}
        /* Modern Toggle Buttons */
        .toggle-button-cover {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            width: 220px;
            padding: 16px 20px;
            margin: 8px 0;
            background: var(--dark-bg);
            backdrop-filter: var(--blur-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            transition: var(--transition);
            box-sizing: border-box;
        }

        .toggle-button-cover:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }

        .knobs, .layer {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }

        .toggle-button-cover img {
            width: 28px;
            height: 28px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
            transition: var(--transition);
        }

        .toggle-button-cover:hover img {
            transform: scale(1.1);
        }

        .button {
            position: relative;
            width: 60px;
            height: 30px;
            overflow: hidden;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            transition: var(--transition);
        }

        .button.r, .button.r .layer {
            border-radius: 20px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .button:hover {
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }
        /* Modern Checkbox & Toggle */
        .checkbox {
            position: relative;
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            opacity: 0;
            cursor: pointer;
            z-index: 3;
        }

        .knobs {
            z-index: 2;
        }

        .layer {
            width: 100%;
            background: linear-gradient(135deg,
                rgba(107, 114, 128, 0.3),
                rgba(75, 85, 99, 0.3));
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
        }

        /* Modern Toggle Switch */
        #button-4 .knobs:before {
            content: "ON";
            position: absolute;
            top: 3px;
            left: 32px;
            width: 24px;
            height: 24px;
            color: var(--text-primary);
            text-align: center;
            line-height: 24px;
            background: linear-gradient(135deg, var(--success-color), var(--info-color));
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 8px;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        #button-4 .checkbox:active + .knobs:before {
            width: 28px;
            margin-left: -2px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        /* Toggle OFF State */
        #button-4 .checkbox:checked:active + .knobs:before {
            margin-left: 0px;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
        }

        #button-4 .checkbox:checked + .knobs:before {
            content: "OFF";
            left: 4px;
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        #button-4 .checkbox:checked ~ .layer {
            background: linear-gradient(135deg,
                rgba(239, 68, 68, 0.2),
                rgba(220, 38, 38, 0.2));
        }

        /* Modern Color Selection Dropdown */
        #selection {
            position: absolute;
            left: 50%;
            top: 320px;
            transform: translateX(-50%);
            width: 280px;
            height: 45px;
            background: var(--dark-bg);
            backdrop-filter: var(--blur-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            box-shadow: var(--shadow-lg);
            cursor: pointer;
            transition: var(--transition);
            outline: none;
            padding: 0 16px;
        }

        #selection:hover {
            background: rgba(17, 24, 39, 0.98);
            border-color: var(--primary-color);
            box-shadow: var(--shadow-xl);
        }

        #selection:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        #selection option {
            background: var(--dark-bg);
            color: var(--text-primary);
            font-size: 13px;
            font-weight: 500;
            padding: 8px 12px;
            border: none;
        }

        #selection option:hover {
            background: var(--primary-color);
        }
        /* Modern Color Picker */
        .color-block {
            max-width: 300px;
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 16px;
        }

        .jQWCP-wWidget {
            position: relative;
            box-sizing: content-box;
            background: var(--dark-bg);
            backdrop-filter: var(--blur-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            padding: 20px;
            z-index: 1001;
            width: 280px !important;
            touch-action: none;
            transition: var(--transition);
        }

        .jQWCP-wWidget:focus {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
            border-color: var(--primary-color);
        }

        .jQWCP-wWidget:hover {
            box-shadow: var(--shadow-xl);
        }

        .jQWCP-wWidget.jQWCP-block {
            position: absolute;
            left: 50%;
            top: 380px;
            transform: translateX(-50%);
            border-radius: var(--border-radius);
        }

        /* Color Picker Section */
        .color-picker-section {
            margin-top: 32px;
            padding: 24px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            backdrop-filter: blur(8px);
        }

        .color-picker-section h3 {
            margin: 0 0 16px 0;
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .color-picker-section h3::before {
            content: '🎨';
            font-size: 20px;
        }

        .color-picker-section p {
            margin: 0 0 20px 0;
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
        }

        /* Enhanced Color Picker Styling */
        .jQWCP-wWidget .jQWCP-wWheel {
            border-radius: 50%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .jQWCP-wWidget .jQWCP-wPreview {
            border-radius: var(--border-radius);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .jQWCP-wWidget .jQWCP-wAlphaSlider,
        .jQWCP-wWidget .jQWCP-wValueSlider {
            border-radius: 4px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        /* Color Selection Container */
        .color-selection-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .color-selection-container label {
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            margin-bottom: 8px;
            display: block;
        }

        .color-picker-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        /* Enhanced Select Styling */
        #selection optgroup {
            background: var(--dark-bg);
            color: var(--text-secondary);
            font-weight: 600;
            font-size: 13px;
            padding: 8px 12px;
        }

        #selection option {
            background: var(--dark-bg);
            color: var(--text-primary);
            padding: 10px 16px;
            border: none;
            transition: var(--transition);
        }

        #selection option:hover {
            background: var(--primary-color);
            color: white;
        }

        #selection option:checked {
            background: var(--primary-color);
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .Panl {
                width: 95vw;
                height: 90vh;
            }

            .header {
                height: 80px;
                padding: 0 24px;
            }

            .header-center .quick-actions {
                gap: 6px;
            }

            .quick-btn {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }
        }

        @media (max-width: 768px) {
            .Panl {
                width: 98vw;
                height: 95vh;
                border-radius: 12px;
            }

            .header {
                flex-direction: column;
                height: auto;
                padding: 16px;
                gap: 12px;
            }

            .header-left, .header-center, .header-right {
                width: 100%;
                justify-content: center;
            }

            .tab-nav {
                flex-wrap: wrap;
                gap: 4px;
            }

            .tab-btn {
                flex: 1;
                min-width: calc(50% - 2px);
                padding: 10px 12px;
                font-size: 12px;
            }

            .style-grid, .elements-grid {
                grid-template-columns: 1fr;
            }

            .Panl .content {
                padding: 16px;
                height: calc(100% - 120px);
            }
        }

        @media (max-width: 480px) {
            .Panl {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
                top: 0;
                left: 0;
                transform: none;
            }

            .header {
                border-radius: 0;
            }

            .tab-btn {
                min-width: 100%;
                margin-bottom: 4px;
            }
        }

        /* Animation for Panel Opening */
        .Panl.opening {
            animation: panelOpen 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes panelOpen {
            from {
                opacity: 0;
                transform: translate(-50%, -60%) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        /* إصلاح مشاكل الأشكال والأماكن */
        .Panl * {
            box-sizing: border-box;
        }

        /* منع تداخل العناصر */
        .Panl .content > * {
            position: relative;
            z-index: 1;
        }

        /* إصلاح مشكلة المكعبات السوداء */
        .Panl .content::before,
        .Panl .content::after,
        .panel-section::before,
        .panel-section::after {
            display: none !important;
        }

        /* تحسين عرض العناصر */
        .style-grid > *,
        .elements-grid > *,
        .color-items > *,
        .presets-grid > * {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.08) 100%) !important;
            border: 1px solid rgba(255, 255, 255, 0.15) !important;
            backdrop-filter: blur(8px) !important;
        }

        /* إزالة أي خلفيات سوداء */
        .Panl [style*="background: black"],
        .Panl [style*="background: #000"],
        .Panl [style*="background-color: black"],
        .Panl [style*="background-color: #000"] {
            background: transparent !important;
            background-color: transparent !important;
        }

        /* تحسين الشفافية */
        .Panl .tab-content {
            background: transparent;
        }

        /* إصلاح مشكلة الحركة */
        .Panl {
            user-select: none;
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }

        .Panl .header {
            cursor: default !important;
        }

        .Panl .header * {
            pointer-events: auto;
        }

        /* Loading Animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--glass-border);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
.jQWCP-wWheel {
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    position: relative;
    float: left;
    width: 100px;
    height: 100px;
    -webkit-border-radius: 90px;
    -moz-border-radius: 50%;
    border-radius: 50%;
    border: solid 2px #ffffff;
    margin: 5px;
    margin-right: 10px;
    transition: border .15s;
    cursor: crosshair;
    box-shadow: rgb(0 0 0 / 25%) 0 .0625em 3px,rgb(0 0 0 / 55%) 0 .025em .5em,rgb(255 255 255 / 10%) 0 0 0 1px
}
.jQWCP-wWheelOverlay {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0;
    -webkit-border-radius: 90px;
    -moz-border-radius: 50%;
    border-radius: 50%
}
.jQWCP-wWheelCursor {
    width: 8px;
    height: 8px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -6px;
    cursor: crosshair;
    border: solid 2px #fff;
    box-shadow: 1px 1px 2px #000;
    border-radius: 50%
}

.jQWCP-slider-wrapper,.jQWCP-wPreview {
    position: relative;
    width: 13px;
    height: 80px;
    float: left;
    margin-top: 5px;
    margin-right: 9px
}
.jQWCP-slider-wrapper:last-child,.jQWCP-wPreview:last-child,.jQWCP-wWheel:last-child {
    margin-right: 0
}
.jQWCP-slider,.jQWCP-wPreviewBox {
    position: relative;
    width: 100%;
    height: 120%;
    border: solid 1px #7b7b7b;
    margin: -1px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    transition: border .15s;
    box-shadow: rgb(0 0 0 / 25%) 0 .0625em 3px,rgb(0 0 0 / 55%) 0 .025em .5em,rgb(255 255 255 / 10%) 0 0 0 1px
}
.jQWCP-scursor {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 6px;
    margin: -5px -1px -5px -3px;
    cursor: crosshair;
    border: solid 2px #fff;
    box-shadow: 1px 1px 2px #000;
    border-radius: 4px
}
.jQWCP-wAlphaSlider,.jQWCP-wPreviewBox {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAABlBMVEVAQEB/f39eaJUuAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3QYRBDgK9dKdMgAAABl0RVh0Q29tbWVudABDcmVhdGVkIHdpdGggR0lNUFeBDhcAAAARSURBVAjXY/jPwIAVYRf9DwB+vw/x6vMT1wAAAABJRU5ErkJggg==) center center
}
.jQWCP-overlay {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1000
}
/* */

.popopp {
    position: relative;
	left: 77%;
    top: 2.5vh;
	border-radius: 7%;
    border-bottom-left-radius: 70px;
    border-top-left-radius: 70px;
	width: 350px;
	height: 95px;
	z-index: 7;
    background-image: linear-gradient(to left, rgba(255, 0, 0, 0), rgba(255, 208, 0, 0.6));

	box-shadow: rgba(0, 0, 0, .5) 0 0 4px 0;
}
.right-avatar-top-left-list {
	width: 175px;
	padding: 2px 22px 2px 0
}
.right-avatar-top-left-list ul li {
	height: 20px;
    position: relative;
    left: 120px;
    top: -97px;
	padding-left: 5px;
	margin-bottom: 2px;
	border-left: 3px solid #fff;
	background: linear-gradient(to left, rgba(255, 0, 0, 0), rgba(255, 208, 0, 0.329));
	border-radius: 20px 0 0 20px
}
.pop61 img {
    position: relative;
	left: 15vw;
    top: -10.2vh;
	border-radius: 7%;
    border-bottom-left-radius: 70px;
    border-top-left-radius: 70px;
	width: 110px;
	height: 100px;
	z-index: 7;

	box-shadow: rgba(0, 0, 0, .5) 0 0 4px 0;
}

ul {
    list-style-type: none; /* هذا يعطل ظهور النقاط */
    padding: 0;
    margin: 0;
}
ul li:last-child {
	margin-bottom: 0
}

 ul li p {
    left: 30px;
	text-shadow: rgba(0, 0, 0, .3) 0 0 2px;
	font-size: 14px;
	margin-left: -10px;
    color: #ffffff;
    position: relative;
    top: -35px
    
    
}
#jover {
	border-radius: 7%;
    border-bottom-left-radius: 70px;
    border-top-left-radius: 70px;
    
}
#Hud .PlayerInfo6 {
    position: relative;
    display: flex;
    height: 30px;
    border-radius: 20px;
    padding: 0px 10px 0px 10px;
    background-image: linear-gradient(to left,rgba(255,0,0,0),rgb(117 117 117 / 50%));
    box-shadow: rgb(0 0 0 / 25%) 0 .0625em 3px,rgb(0 0 0 / 55%) 0 .025em .5em,rgb(255 255 255 / 10%) 0 0 0 1px inset;
    font-weight: 600;
}
#Hud .PlayerInfo6 i {
    display: flex;
    align-items: center;
    margin-right: 10px;
    color: #fff;
}
#Hud .PlayerInfo6 span {
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: auto !important;
    min-width: 150px;
    font-size: 15px;
    font-weight: 600;
    color: #fff;
    text-shadow: 0px 0px 6px rgb(0 0 0 / 63%),0 8px 13px rgb(0 0 0 / 10%),0 18px 23px rgb(0 0 0 / 10%);
}

.avatar {
    position: relative;
    left: 80%;
    top: -1.5px;
        height: 100px;
        width: 100px;
        border-radius: 9999px;
        border: 1px solid var(--hero-accent);
        overflow: hidden;
      }
      
      .avatar img {
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
      .pop12 {
        position: relative;

        left: 20px;
      }
	</style>
</head>
<body>
	<script>
		Black = {};
$("body").html(`
    <!-- Modern HUD Background Effect -->
    <div class="hud-bg-effect"></div>

    <div id="Hud">
        <div class="watermarkBox cin">
            <div id="watermark"><div class="watermark glow-effect" id="watermarkx">🌙 Night HUD</div></div>
            <div class="Time glow-effect" id="time">10:40</div>
            <div class="Online glow-effect" id="count">👥 Online: 1</div>
        </div>

        <div id="cinematic"></div>
        <div class="pop11" style="position: absolute; left: 1vw; top:-50px;">
        <div class="ServerAvater cin" id="serverlogo" style="width: 138px; height: 138px;">
            <img style="width: 130px; height: 125px;" src="https://media.discordapp.net/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=https://media.discordapp.net/attachments/1272828176166555751/1276694290399101060/gqbqc6S.png?ex=685f3236&is=685de0b6&hm=2cd4d828be9b53eff493885ff8200a811516e3e8309659a6bbe2e11ce8facd00&=&format=webp&quality=lossless&width=400&height=287" alt="">
        </div>
		        <div id="Job" class="Info cin" style="top: 7vh; left: 83vw;">
            <div class="PlayerInfo1" >
                <i class="fas fa-briefcase"></i>
                <span>..جاري التحميل</span>
            </div>
        </div>

        <div id="Bank" class="Info cin" style="top:14vh; left: 83vw;">
            <div class="PlayerInfo1">
                <i class="fa fa-university"></i>
                <span>..جاري التحميل</span>
            </div>
        </div>
        <div id="wallet" class="Info cin" style="top:10.5vh; left: 83vw;">
            <div class="PlayerInfo1">
               <i class="fa-solid fa-wallet"></i>
                <span>..جاري التحميل</span>
            </div>
        </div>
        <div id="id" class="Info cin" style="top:17.5vh; left: 83vw; ">
            <div class="PlayerInfo1">
                <i class="fa-solid fa-id-card"></i>
                <span>..جاري التحميل</span>
            </div>
        </div>
        </div>
                <div class="pop13">
        <div class="ServerAvater cin" id="serverlogo" style="position: absolute; height: 145px; width: 145px; left: 91vw; top: 25px; border-radius: 70%; box-shadow: rgba(255, 208, 0, 0.8) 0 0 15px 0; background: linear-gradient(to top, rgba(0, 0, 0, 0.0), rgba(0, 0, 0, 0.0));">
            <img src="https://media.discordapp.net/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=a0ca111b3f9ec5eafe653e46d8dc8808b813154a91b4f3c86f42b800c787c9b7&=" alt="" style="background: linear-gradient(to top, rgba(0, 0, 0, 0.0), rgba(0, 0, 0, 0.0));  height: 135px; width: 135px;" >
        </div>
		        <div id="Job" class="Info cin" style="top: 3vh; left: 82vw;">
            <div class="PlayerInfo" style="border-radius: 18px; border: solid 2px rgba(255, 208, 0, 0.719); box-shadow: rgba(255, 208, 0, 0.8) 0 0 5px 0; background: linear-gradient(to right, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4));">
                <i class="fas ffa-briefcase"></i>
                <span>Job</span>
            </div>
        </div>

        <div id="Bank" class="Info cin" style="top:7vh;left: 82vw;">
            <div class="PlayerInfo" style="border-radius: 18px; border: solid 2px rgba(255, 208, 0, 0.719); box-shadow: rgba(255, 208, 0, 0.8) 0 0 5px 0; background: linear-gradient(to right, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4));">
                <i class="fa fa-university"></i>
                <span>Bank</span>
            </div>
        </div>
        <div id="wallet" class="Info cin" style="top:11vh; left: 82vw;">
            <div class="PlayerInfo" style="border-radius: 18px; border: solid 2px rgba(255, 208, 0, 0.719); box-shadow: rgba(255, 208, 0, 0.8) 0 0 5px 0; background: linear-gradient(to right, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4));">
               <i class="fa-solid fa-wallet"></i>
                <span>wallet</span>
            </div>
        </div>
        <div id="id" class="Info cin" style="top:15vh; left: 82vw;">
            <div class="PlayerInfo" style="border-radius: 18px; border: solid 2px rgba(255, 208, 0, 0.719); box-shadow: rgba(255, 208, 0, 0.8) 0 0 5px 0; background: linear-gradient(to right, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4));">
                <i class="fa-solid fa-id-card"></i>
                <span>ID</span>
            </div>
        </div>
        </div>
                        <div class="pop15">
        <div class="ServerAvater cin" id="serverlogo" style="position: absolute; height: 165px; width: 165px; left: 90vw; top: 20px; border-radius: 70%; background: linear-gradient(to top, rgba(0, 0, 0, 0.0), rgba(0, 0, 0, 0.0));">
            <img src="https://media.discordapp.net/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=https://media.discordapp.net/attachments/1272828176166555751/1276694290399101060/gqbqc6S.png?ex=685f3236&is=685de0b6&hm=2cd4d828be9b53eff493885ff8200a811516e3e8309659a6bbe2e11ce8facd00&=&format=webp&quality=lossless&width=400&height=287" alt="" style="background: linear-gradient(to top, rgba(0, 0, 0, 0.0), rgba(0, 0, 0, 0.0));  height: 165px; width: 165px;" >
        </div>
		        <div id="Job" class="Info cin" style="top:7vh;left: 83vw;">
            <div class="PlayerInfo5" style="border-radius: 18px;"">
                <i class="fas fa-briefcase"></i>
                <span>Job</span>
            </div>
        </div>

        <div id="Bank" class="Info cin" style="top:10.7vh; left: 83vw;">
            <div class="PlayerInfo5" style="border-radius: 18px;">
                <i class="fa fa-university"></i>
                <span>Bank</span>
            </div>
        </div>
        <div id="wallet" class="Info cin" style="top:14.4vh; left: 84.5vw;">
            <div class="PlayerInfo5" style="border-radius: 18px;">
               <i class="fa-solid fa-wallet"></i>
                <span>wallet</span>
            </div>
        </div>
        <div id="id" class="Info cin" style="top: 3.4vh; left: 84.5vw;">
            <div class="PlayerInfo5" style="border-radius: 18px;">
                <i class="fa-solid fa-id-card"></i>
                <span>ID</span>
            </div>
        </div>
        </div>
                                <div class="pop16">
        <div class="ServerAvater1 cin" id="serverlogo" style="position: absolute; height: 105px; width: 105px; left: 85.3vw; top: 100px; border-radius: 70%; background: linear-gradient(to top, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5));">
            <img src="https://media.discordapp.net/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=a0ca111b3f9ec5eafe653e46d8dc8808b813154a91b4f3c86f42b800c787c9b7&=" alt="" style="position: absolute; background: linear-gradient(to top, rgba(0, 0, 0, 0.0), rgba(0, 0, 0, 0.0));  height: 95px; width: 95px; top:5px; " >
        </div>
		        <div id="Job" class="Info cin" style="    top: 27vh;
    left: 88vw;">
            <div class="PlayerInfo6" style="border-radius: 18px;"">
                <i class="fas fa-briefcase"></i>
                <span>Job</span>
            </div>
        </div>

        <div id="Bank" class="Info cin" style="top: 23vh;left: 77vw;">
            <div class="PlayerInfo6" style="border-radius: 18px;">
                <i class="fa fa-university"></i>
                <span>Bank</span>
            </div>
        </div>
        <div id="wallet" class="Info cin" style="    top: 27vh;
    left: 77vw;">
            <div class="PlayerInfo6" style="border-radius: 18px;">
               <i class="fa-solid fa-wallet"></i>
                <span>wallet</span>
            </div>
        </div>
        <div id="id" class="Info cin" style="    top: 23vh;
    left: 88vw;">
            <div class="PlayerInfo6" style="border-radius: 18px;">
                <i class="fa-solid fa-id-card"></i>
                <span>ID</span>
            </div>
            
        </div>
 
            
        </div>
                        <div class="pop14">
        <div class="ServerAvater cin" id="serverlogo" style="  width: 120px; height: 120px; position: absolute; left: 86.8%; top: 2%; border-radius: 70%; box-shadow: rgba(255, 208, 0, 0.8) 0 0 15px 0; background: linear-gradient(to top, rgba(0, 0, 0, 0.0), rgba(0, 0, 0, 0.0));">
            <img src="https://media.discordapp.net/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=a0ca111b3f9ec5eafe653e46d8dc8808b813154a91b4f3c86f42b800c787c9b7&=" alt="" style="background: linear-gradient(to top, rgba(0, 0, 0, 0.0), rgba(0, 0, 0, 0.0));">
        </div>

		        <div id="Job" class="Info cin" style=" position: absolute; top: 15.7%; left: 80.5%;">
            <div class="PlayerInfo3" style="
            border-radius: 18px; 
            border: solid 2px rgba(255, 208, 0, 0.719);  
            background-color: #a5a5a533;
background: radial-gradient(202% 126.74% at 56% 51.16%, rgba(0, 0, 0, 0.20) 0%, rgba(255, 208, 0, 0.8) 93.98%);
            ">
            <i class="fas fa-briefcase"></i>
                <span>Job</span>
            </div>
        </div>

        <div id="Bank" class="Info cin" style=" position: absolute; top: 19%; left: 90.3%;">
            <div class="PlayerInfo3" style="
            border-radius: 18px; 
            border: solid 2px rgba(255, 208, 0, 0.719);  
            background-color: #a5a5a533;
background: radial-gradient(202% 126.74% at 56% 51.16%, rgba(0, 0, 0, 0.20) 0%, rgba(255, 208, 0, 0.8) 93.98%);
            ">                <i class="fa fa-university"></i>
                <span>Bank</span>
            </div>
        </div>
        <div id="wallet" class="Info cin" style=" position: absolute; top: 19%; left: 80.5%;">
            <div class="PlayerInfo3" style="
            border-radius: 18px; 
            border: solid 2px rgba(255, 208, 0, 0.719);  
            background-color: #a5a5a533;
background: radial-gradient(202% 126.74% at 56% 51.16%, rgba(0, 0, 0, 0.20) 0%, rgba(255, 208, 0, 0.8) 93.98%);
            ">               <i class="fa-solid fa-wallet"></i>
                <span>wallet</span>
            </div>
        </div>
        <div id="id" class="Info cin" style=" position: absolute; top: 15.7%; left: 90.3%;">
            <div class="PlayerInfo3" style="
            border-radius: 18px; 
            border: solid 2px rgba(255, 208, 0, 0.719);  
            background-color: #a5a5a533;
background: radial-gradient(202% 126.74% at 56% 51.16%, rgba(0, 0, 0, 0.20) 0%, rgba(255, 208, 0, 0.8) 93.98%);
            ">                <i class="fa-solid fa-id-card"></i>
                <span>ID</span>
            </div>
        </div>
        </div>
                <div class="pop12">

        <div class="popopp">

		<div class="ServerAvater1 cin" id="serverlogo">
            <img src="https://media.discordapp.net/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=a0ca111b3f9ec5eafe653e46d8dc8808b813154a91b4f3c86f42b800c787c9b7&=" alt="">

            </div>
         <div class="avatar">
              <img id="user-avatar" src="" alt="Avatar" />
            </div>
<div class="right-avatar-top-left-list">
    <ul class="ps-0 mb-0">
        <li class="d-flex align-items-center">
            <img src="./img/hud/icon_Map_Signs.png" alt="icon_Map_Signs">
            <p id="server-name" class="mb-0 font-sf-sbld server-name-text">جاري التحميل</p>
        </li>
        <li class="d-flex align-items-center online-players">
            <img src="./img/hud/icon_Globe_with_Africa_shown.png" alt="icon_Globe_with_Africa_shown">
            <p id="iddd" class="online-players-text mb-0 font-sf-sbld">جاري التحميل</p>
        </li>
        <li class="d-flex align-items-center">
            <img src="./img/hud/icon_Discord.png" alt="icon_Discord">
            <p id="discord51" class="mb-0 font-sf-sbld">جاري التحميل</p>
        </li>
        <li class="d-flex align-items-center">
            <img src="./img/hud/icon_Gamepad.png" alt="icon_Gamepad">
            <p id="timeee" class="timeee">جاري التحميل</p>
        </li>
    </ul>
</div>
            </div>

    <div id="Job" class="Info cin" style="top:165px">
        <div class="PlayerInfo2 " style="border-bottom-left-radius: 15px; border-bottom-right-radius: 15px;">
            <i class="fas fa-briefcase"></i>
            <span>عاطل</span>
        </div>
    </div>
    <div id="Bank" class="Info cin" style="top:130px">
        <div class="PlayerInfo2" style="border-top-left-radius: 15px; border-top-right-radius: 15px;">
            <i class="fa fa-university"></i>
            <span>10000</span>
        </div>
    </div>
    <div id="wallet" class="Info cin" style="top:165px">
        <div class="PlayerInfo2" style="border-bottom-left-radius: 15px; border-bottom-right-radius: 15px;">
           <i class="fa-solid fa-wallet"></i>
            <span></span>
        </div>
    </div>
    <div id="id" class="Info cin" style="top:130px">
        <div class="PlayerInfo2 " style="border-top-left-radius: 15px; border-top-right-radius: 15px;">
            <i class="fa-solid fa-id-card"></i>
            <span></span>
        </div>
    </div>


        </div>



        <div class="Weapon cin" id="Weapon">
            <div class="img">
                <img src="./img/weapons/WEAPON_ADVANCEDRIFLE.png" alt="">
            </div>
            <div class="ammo">
                <img src="./img/weapons/weapon-bullets.png" alt="">
                <span>100/7</span>
            </div>
        </div>

        <div class="HotKays cin" id="keybinds"></div>

        <div id=map class="outline cin">
			<center>
				<div id="mapborder" class="mapborder">
                    </div>
                
			</center>
		</div>


        <div class="carStats">
			<div class="counter" id="speedometer">
				<div class="counter2" id="idCounter2">
					<div class="speedometer">
						<svg class="circle" width="50" height="50">
							<circle class="outer" stroke="#000000" stroke-width="0.9" style="stroke-opacity: 0.5;" fill="transparent" r="18" cx="25" cy="25" stroke-dasharray="56 45" stroke-dashoffset="0"></circle>
							<circle class="backgroundCircle" stroke="#000000" stroke-width="4" style="stroke-opacity: 0.4;" fill="transparent" r="18" cx="25" cy="25" stroke-dasharray="56 45" stroke-dashoffset="0"></circle>
							<circle class="progress progress-speed" stroke="url(#gradient)" stroke-width="2.5" style="stroke-opacity: 1;" fill="transparent" r="18" cx="25" cy="25" stroke-dasharray="60 90" stroke-dashoffset="0"></circle>
							<defs>
								<linearGradient id="gradient">
									<stop offset="30%" stop-color="red"></stop>
									<stop offset="100%" stop-color="#82D205"></stop>
								</linearGradient>
							</defs>
						</svg>
						<div class="text">
							<span id="speed-color" class="speed">0</span>
							<span id="speed-color" class="mph">KM/H</span>
						</div>
					</div>
				</div>
			</div>

			
				<svg class="circle" id="fuel" width="40" height="40">
					<circle class="progressplayer fuel" id="fuel-circle" stroke="#ffffff" stroke-width="3.5" style="stroke-opacity: 0.5;" fill="transparent" r="14" cx="16" cy="16" stroke-dasharray="30 70" stroke-dashoffset="0" />
					<image class="white-icon fuel" href="img/hud/fuel.png" height="14" width="14" x="9" y="16"></image>
				</svg>

				<svg class="circle" id="seatbelt" width="40" height="40">
					<circle class="progressplayer seatbelt" id="seatbelt-circle" stroke="red" stroke-width="3.5" style="stroke-opacity: 0.5;" fill="transparent" r="14" cx="16" cy="16" />
					<image class="seatbelt-img" href="img/hud/seatbelt.png" height="14" width="14" x="9" y="16"></image>
				</svg>

				<svg class="circle" id="lights" width="40" height="40">
					<circle class="progressplayer lights" id="lights-circle" stroke="red" stroke-width="3.5" style="stroke-opacity: 0.5;" fill="transparent" r="14" cx="16" cy="16" />
					<image class="lights-img" href="img/hud/lowbeamBlack.png" height="14" width="14" x="9" y="16"></image>
				</svg>
			
		</div>

        <div class="postals-container cin" id=postals style="display:block;">
            <img src="img/hud/icon_location-arrow.png" alt="icon_location-arrow">

            				<span id=nearest-postal >5048

			<div>
			</div>
		</div>








 <div class="k1" style="display:block;">


        		<svg class="circle cin" height=60 width=60 id=health>
            <circle class="progressplayer health" cx=22 cy=22 fill="rgba(204, 60, 46, 0.7)" r=18 stroke="rgb(204, 60, 46)" stroke-width=3.5 style=stroke-opacity:1;stroke-dasharray:113.097,113.097;stroke-dashoffset:-175.301 stroke-dasharray="70 30" stroke-dashoffset=0 id=health-circle></circle>
			<image height=15 width=15 href=img/hud/health.png x=15 y=30 class=a></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=armor>
            <circle class="progressplayer armor" cx=22 cy=22 fill="rgba(0, 142, 255, 0.5)" r=18 stroke="rgb(0, 142, 255)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=armor-circle />

            <image height=15 width=15 href=img/hud/armor.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=hunger>
            <circle class="progressplayer hunger" cx=22 cy=22 fill="rgba(255, 201, 0, 0.5)" r=18 stroke="rgb(255, 201, 0)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=hunger-circle />
			<image height=15 width=15 href=img/hud/hunger.png x=15 y=30 class=a></image>
		</svg> 

		<svg class="circle cin" height=60 width=60 id=thirst>
            <circle class="progressplayer thirst" cx=22 cy=22 fill="rgba(0, 230, 255, 0.4)" r=18 stroke="rgb(0, 230, 255)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=thirst-circle />
			<image height=15 width=15 href=img/hud/thirst.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=stamina>
            <circle class="progressplayer stamina" cx=22 cy=22 fill="rgb(166, 173, 67, 0.7)" r=18 stroke="rgb(237, 255, 0)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=stamina-circle />
			<image height=15 width=15 href=img/hud/stamina.png x=15 y=30 class=white-icon></image>
		</svg> 
					</div>
 <div class="k2" style="display:none;">

		<svg class="circle cin" height=60 width=60 id=health>
            <circle class="progressplayer health" cx=22 cy=22 fill="rgba(0, 0, 0, 1)" r=18 stroke="rgb(204, 60, 46)" stroke-width=3.5 style=stroke-opacity:1;stroke-dasharray:113.097,113.097;stroke-dashoffset:-175.301 stroke-dasharray="70 30" stroke-dashoffset=0 id=health-circle></circle>
			<image height=15 width=15 href=img/hud/health.png x=15 y=30 class=a></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=armor>
            <circle class="progressplayer armor" cx=22 cy=22 fill="rgba(0, 0, 0, 1)" r=18 stroke=#3585DA stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=armor-circle />
			<image height=15 width=15 href=img/hud/armor.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=hunger>
            <circle class="progressplayer hunger" cx=22 cy=22 fill="rgba(0, 0, 0, 1)" r=18 stroke=#86fa01c7 stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=hunger-circle />
			<image height=15 width=15 href=img/hud/hunger.png x=15 y=30 class=a></image>
		</svg> 

		<svg class="circle cin" height=60 width=60 id=thirst>
            <circle class="progressplayer thirst" cx=22 cy=22 fill="rgba(0, 0, 0, 1)" r=18 stroke=#00b6e3 stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=thirst-circle />
			<image height=15 width=15 href=img/hud/thirst.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=stamina>
            <circle class="progressplayer stamina" cx=22 cy=22 fill="rgba(0, 0, 0, 1)" r=18 stroke=#FFC100 stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=stamina-circle />
			<image height=15 width=15 href=img/hud/stamina.png x=15 y=30 class=white-icon></image>
		</svg> 
					</div>
                     <div class="k3" style="display:none;">

        		<svg class="circle cin" height=60 width=60 id=health>
            <circle class="progressplayer health" cx=22 cy=22 fill="rgba(0, 0, 0, 0.2)" r=18 stroke="rgb(204, 60, 46)" stroke-width=3.5 style=stroke-opacity:1;stroke-dasharray:113.097,113.097;stroke-dashoffset:-175.301 stroke-dasharray="70 30" stroke-dashoffset=0 id=health-circle></circle>
			<image height=15 width=15 href=img/hud/health.png x=15 y=30 class=a></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=armor>
            <circle class="progressplayer armor" cx=22 cy=22 fill="rgba(0, 0, 0, 0.2)" r=18 stroke="rgb(0, 142, 255)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=armor-circle />
			<image height=15 width=15 href=img/hud/armor.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=hunger>
            <circle class="progressplayer hunger" cx=22 cy=22 fill="rgba(0, 0, 0, 0.2)" r=18 stroke="rgb(255, 201, 0)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=hunger-circle />
			<image height=15 width=15 href=img/hud/hunger.png x=15 y=30 class=a></image>
		</svg> 

		<svg class="circle cin" height=60 width=60 id=thirst>
            <circle class="progressplayer thirst" cx=22 cy=22 fill="rgba(0, 0, 0, 0.2)" r=18 stroke="rgb(0, 230, 255)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=thirst-circle />
			<image height=15 width=15 href=img/hud/thirst.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=stamina>
            <circle class="progressplayer stamina" cx=22 cy=22 fill="rgba(0, 0, 0, 0.2)" r=18 stroke="rgb(255, 197, 0)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=stamina-circle />
			<image height=15 width=15 href=img/hud/stamina.png x=15 y=30 class=white-icon></image>
		</svg> 
					</div>
                     <div class="k4" style="display:none;">
		<svg class="circle cin" height=60 width=60 id=health>
            <circle class="progressplayer health" cx=22 cy=22 fill="rgba(255, 0, 0, 0.6)" r=18 stroke="rgb(255, 0, 0)" stroke-width=3.5 style=stroke-opacity:1;stroke-dasharray:113.097,113.097;stroke-dashoffset:-175.301 stroke-dasharray="70 30" stroke-dashoffset=0 id=health-circle></circle>
			<image height=15 width=15 href=img/hud/health.png x=15 y=30 class=a></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=armor>
            <circle class="progressplayer armor" cx=22 cy=22 fill="rgba(70, 168, 82, 0.741)" r=18 stroke="rgb(70, 168, 82)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=armor-circle />
			<image height=15 width=15 href=img/hud/armor.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=hunger>
            <circle class="progressplayer hunger" cx=22 cy=22 fill="rgba(70, 135, 168, 0.741)" r=18 stroke="rgb(70, 135, 168)"stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=hunger-circle />
			<image height=15 width=15 href=img/hud/hunger.png x=15 y=30 class=a></image>
		</svg> 

		<svg class="circle cin" height=60 width=60 id=thirst>
            <circle class="progressplayer thirst" cx=22 cy=22 fill="rgba(70, 91, 168, 0.741)" r=18 stroke="rgb(70, 91, 168)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=thirst-circle />
			<image height=15 width=15 href=img/hud/thirst.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=stamina>
            <circle class="progressplayer stamina" cx=22 cy=22 fill="rgba(168, 158, 70, 0.8)" r=18 stroke="rgb(255, 251, 0)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=stamina-circle />
			<image height=15 width=15 href=img/hud/stamina.png x=15 y=30 class=white-icon></image>
		</svg> 
					</div>
                     <div class="k5" style="display:none;">

        		<svg class="circle cin" height=60 width=60 id=health>
            <circle class="progressplayer health" cx=22 cy=22 fill="rgba(0, 0, 0, 0.0)" r=18 stroke="rgb(204, 60, 46)" stroke-width=3.5 style=stroke-opacity:1;stroke-dasharray:113.097,113.097;stroke-dashoffset:-175.301 stroke-dasharray="70 30" stroke-dashoffset=0 id=health-circle></circle>
			<image height=15 width=15 href=img/hud/health.png x=15 y=30 class=a></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=armor>
            <circle class="progressplayer armor" cx=22 cy=22 fill="rgba(0, 0, 0, 0.0)" r=18 stroke="rgb(0, 142, 255)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=armor-circle />
			<image height=15 width=15 href=img/hud/armor.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=hunger>
            <circle class="progressplayer hunger" cx=22 cy=22 fill="rgba(0, 0, 0, 0.0)" r=18 stroke="rgb(255, 201, 0)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=hunger-circle />
			<image height=15 width=15 href=img/hud/hunger.png x=15 y=30 class=a></image>
		</svg> 

		<svg class="circle cin" height=60 width=60 id=thirst>
            <circle class="progressplayer thirst" cx=22 cy=22 fill="rgba(0, 0, 0, 0.0)" r=18 stroke="rgb(0, 230, 255)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=thirst-circle />
			<image height=15 width=15 href=img/hud/thirst.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=stamina>
            <circle class="progressplayer stamina" cx=22 cy=22 fill="rgba(0, 0, 0, 0.0)" r=18 stroke="rgb(255, 197, 0)" stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=stamina-circle />
			<image height=15 width=15 href=img/hud/stamina.png x=15 y=30 class=white-icon></image>
		</svg> 
					</div>
   
                     <div class="k7" style="display:none;">

		<svg class="circle cin" height=60 width=60 id=health>
            <circle class="progressplayer health" cx=22 cy=22 fill="rgba(255, 0, 0, 0.1)" r=18 stroke="rgb(204, 60, 46)" stroke-width=3.5 style=stroke-opacity:1;stroke-dasharray:113.097,113.097;stroke-dashoffset:-175.301 stroke-dasharray="70 30" stroke-dashoffset=0 id=health-circle></circle>
			<image height=15 width=15 href=img/hud/health.png x=15 y=30 class=a></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=armor>
            <circle class="progressplayer armor" cx=22 cy=22 fill="#3585da49" r=18 stroke=#3585DA stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=armor-circle />
			<image height=15 width=15 href=img/hud/armor.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=hunger>
            <circle class="progressplayer hunger" cx=22 cy=22 fill="#86fa013a" r=18 stroke=#86fa01c7 stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=hunger-circle />
			<image height=15 width=15 href=img/hud/hunger.png x=15 y=30 class=a></image>
		</svg> 

		<svg class="circle cin" height=60 width=60 id=thirst>
            <circle class="progressplayer thirst" cx=22 cy=22 fill="#00b6e346" r=18 stroke=#00b6e3 stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=thirst-circle />
			<image height=15 width=15 href=img/hud/thirst.png x=15 y=30></image>
		</svg> 
		
		<svg class="circle cin" height=60 width=60 id=stamina>
            <circle class="progressplayer stamina" cx=22 cy=22 fill="#ffbf0028" r=18 stroke=#FFC100 stroke-width=3.5 style=stroke-opacity:1 stroke-dasharray="70 30" stroke-dashoffset=0 id=stamina-circle />
			<image height=15 width=15 href=img/hud/stamina.png x=15 y=30 class=white-icon></image>
		</svg> 
					</div>
                  
        <div class="Panl" id="Panl" style="display: none;">
			<div class="header">
				<div class="header-left">
					<h1 id="title-hud">🌙 Night HUD Pro</h1>
					<span class="version-badge">v2.0</span>
				</div>
				<div class="header-center">
					<div class="quick-actions">
						<button class="quick-btn" id="quick-save" title="حفظ سريع">
							💾
						</button>
						<button class="quick-btn" id="quick-load" title="تحميل سريع">
							📂
						</button>
						<button class="quick-btn" id="quick-reset" title="إعادة تعيين سريع">
							🔄
						</button>
						<button class="quick-btn" id="quick-preview" title="معاينة">
							👁️
						</button>
					</div>
				</div>
				<div class="header-right">
					<div class="options">
						<div class="options-button" id="button-position">
							<img src="./img/hud/position.png" alt="Position">
							<span>📍 المواقع</span>
						</div>
						<div class="options-button" id="button-color">
							<img src="./img/hud/color.png" alt="Color">
							<span>🎨 الألوان</span>
						</div>
						<div class="options-button" id="button-Share">
							<img src="./img/hud/share.png" alt="Share">
							<span>📤 مشاركة</span>
						</div>
					</div>
					<div id="close">
						<img src="./img/hud/close.png" alt="Close" style="width: 20px; height: 20px;">
					</div>
				</div>
			</div>
			<div class="content">
				<!-- شريط التبويب -->
				<div class="tab-container">
					<div class="tab-nav">
						<button class="tab-btn active" data-tab="styles">🎨 الأشكال</button>
						<button class="tab-btn" data-tab="elements">🎮 العناصر</button>
						<button class="tab-btn" data-tab="colors">🌈 الألوان</button>
						<button class="tab-btn" data-tab="advanced">⚙️ متقدم</button>
						<button class="tab-btn" data-tab="presets">📋 القوالب</button>
					</div>

					<!-- تبويب الأشكال -->
					<div class="tab-content active" id="styles-tab">
						<div class="panel-section">
							<h3>🎨 تخصيص الأشكال والتصميم</h3>
							<p>اختر الأشكال المفضلة لعناصر الـ HUD المختلفة</p>
							<div class="style-grid">
								<div class="style-card">
									<div class="style-preview">
										<div class="preview-circle health-preview"></div>
									</div>
									<label for="button-Skl2">❤️ شكل الصحة والدرع:</label>
									<select class="modern-select" id="button-Skl2">
										<option value="none" selected>اختر الشكل...</option>
										<option value="k1">🔴 الشكل الدائري الكلاسيكي</option>
										<option value="k2">🟠 الشكل المربع الحديث</option>
										<option value="k3">🟡 الشكل السداسي المتطور</option>
										<option value="k4">🟢 الشكل المتدرج الأنيق</option>
										<option value="k5">🔵 الشكل الحديث المبسط</option>
										<option value="k7">🟣 الشكل المتطور المتقدم</option>
									</select>
								</div>

								<div class="style-card">
									<div class="style-preview">
										<div class="preview-info money-preview">$1,234</div>
									</div>
									<label for="button-skl1">💰 شكل المعلومات المالية:</label>
									<select class="modern-select" id="button-skl1">
										<option value="pop12" selected>🔸 الشكل الافتراضي</option>
										<option value="pop11">🔹 الشكل الحديث الأنيق</option>
										<option value="pop13">🔶 الشكل المتقدم الفاخر</option>
										<option value="pop14">🔷 الشكل الأنيق المبسط</option>
										<option value="pop15">🔸 الشكل المبسط الواضح</option>
										<option value="pop16">🔹 الشكل الفاخر المتطور</option>
									</select>
								</div>

								<div class="style-card">
									<div class="style-preview">
										<div class="preview-map"></div>
									</div>
									<label for="map-toggle-btn">🗺️ شكل الخريطة:</label>
									<div class="map-controls">
										<button class="modern-button" id="map-toggle-btn">
											<span>🔄 تبديل الشكل</span>
										</button>
										<div class="map-options">
											<label class="radio-option">
												<input type="radio" name="map-shape" value="circle" checked>
												<span>⭕ دائري</span>
											</label>
											<label class="radio-option">
												<input type="radio" name="map-shape" value="square">
												<span>⬜ مربع</span>
											</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- تبويب العناصر -->
					<div class="tab-content" id="elements-tab">
						<div class="panel-section">
							<h3>🎮 التحكم في عناصر الـ HUD</h3>
							<p>فعل أو أوقف عناصر الـ HUD حسب احتياجاتك</p>

							<div class="elements-grid">
								<!-- عناصر الصحة والحالة -->
								<div class="element-category">
									<h4>🩺 الصحة والحالة</h4>
									<div class="element-list">
										<div class="element-item">
											<div class="element-info">
												<span class="element-icon">❤️</span>
												<div class="element-details">
													<span class="element-name">الصحة</span>
													<span class="element-desc">شريط الحياة الأحمر</span>
												</div>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="health-toggle" data-for="health">
												<label for="health-toggle"></label>
											</div>
										</div>

										<div class="element-item">
											<div class="element-info">
												<span class="element-icon">🛡️</span>
												<div class="element-details">
													<span class="element-name">الدرع</span>
													<span class="element-desc">شريط الحماية الأزرق</span>
												</div>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="armor-toggle" data-for="armor">
												<label for="armor-toggle"></label>
											</div>
										</div>

										<div class="element-item">
											<div class="element-info">
												<span class="element-icon">🍔</span>
												<div class="element-details">
													<span class="element-name">الجوع</span>
													<span class="element-desc">مؤشر الطعام الأصفر</span>
												</div>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="hunger-toggle" data-for="hunger">
												<label for="hunger-toggle"></label>
											</div>
										</div>

										<div class="element-item">
											<div class="element-info">
												<span class="element-icon">💧</span>
												<div class="element-details">
													<span class="element-name">العطش</span>
													<span class="element-desc">مؤشر الماء الأزرق</span>
												</div>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="thirst-toggle" data-for="thirst">
												<label for="thirst-toggle"></label>
											</div>
										</div>

										<div class="element-item">
											<div class="element-info">
												<span class="element-icon">🏃</span>
												<div class="element-details">
													<span class="element-name">القدرة على التحمل</span>
													<span class="element-desc">مؤشر الطاقة الأخضر</span>
												</div>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="stamina-toggle" data-for="stamina">
												<label for="stamina-toggle"></label>
											</div>
										</div>
									</div>
								</div>

								<!-- المعلومات المالية -->
								<div class="element-category">
									<h4>💰 المعلومات المالية</h4>
									<div class="element-list">
										<div class="element-item">
											<div class="element-info">
												<span class="element-icon">💵</span>
												<div class="element-details">
													<span class="element-name">المحفظة</span>
													<span class="element-desc">النقود في الجيب</span>
												</div>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="wallet-toggle" data-for="wallet">
												<label for="wallet-toggle"></label>
											</div>
										</div>

										<div class="element-item">
											<div class="element-info">
												<span class="element-icon">🏦</span>
												<div class="element-details">
													<span class="element-name">البنك</span>
													<span class="element-desc">الرصيد البنكي</span>
												</div>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="bank-toggle" data-for="Bank">
												<label for="bank-toggle"></label>
											</div>
										</div>

										<div class="element-item">
											<div class="element-info">
												<span class="element-icon">💼</span>
												<div class="element-details">
													<span class="element-name">الوظيفة</span>
													<span class="element-desc">اسم الوظيفة الحالية</span>
												</div>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="job-toggle" data-for="Job">
												<label for="job-toggle"></label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- تبويب الألوان المتقدم -->
					<div class="tab-content" id="colors-tab">
						<div class="panel-section">
							<h3>🌈 تخصيص الألوان المتقدم</h3>
							<p>اختر الألوان المثالية لكل عنصر في الـ HUD مع معاينة فورية</p>

							<div class="color-categories">
								<!-- عناصر الصحة والحالة -->
								<div class="color-category">
									<h4>🩺 ألوان الصحة والحالة</h4>
									<div class="color-items">
										<div class="color-item">
											<div class="color-preview">
												<div class="preview-circle health-color-preview"></div>
											</div>
											<div class="color-controls">
												<label>❤️ لون الصحة</label>
												<div class="color-input-group">
													<input type="color" id="health-color-picker" value="#cc3c2e">
													<input type="text" id="health-color-text" value="#cc3c2e" placeholder="#cc3c2e">
													<button class="color-reset-btn" data-target="health">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-circle armor-color-preview"></div>
											</div>
											<div class="color-controls">
												<label>🛡️ لون الدرع</label>
												<div class="color-input-group">
													<input type="color" id="armor-color-picker" value="#3585da">
													<input type="text" id="armor-color-text" value="#3585da" placeholder="#3585da">
													<button class="color-reset-btn" data-target="armor">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-circle hunger-color-preview"></div>
											</div>
											<div class="color-controls">
												<label>🍔 لون الجوع</label>
												<div class="color-input-group">
													<input type="color" id="hunger-color-picker" value="#86fa01">
													<input type="text" id="hunger-color-text" value="#86fa01" placeholder="#86fa01">
													<button class="color-reset-btn" data-target="hunger">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-circle thirst-color-preview"></div>
											</div>
											<div class="color-controls">
												<label>💧 لون العطش</label>
												<div class="color-input-group">
													<input type="color" id="thirst-color-picker" value="#00b6e3">
													<input type="text" id="thirst-color-text" value="#00b6e3" placeholder="#00b6e3">
													<button class="color-reset-btn" data-target="thirst">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-circle stamina-color-preview"></div>
											</div>
											<div class="color-controls">
												<label>🏃 لون القدرة على التحمل</label>
												<div class="color-input-group">
													<input type="color" id="stamina-color-picker" value="#ffc100">
													<input type="text" id="stamina-color-text" value="#ffc100" placeholder="#ffc100">
													<button class="color-reset-btn" data-target="stamina">🔄</button>
												</div>
											</div>
										</div>
									</div>
								</div>

								<!-- المعلومات المالية -->
								<div class="color-category">
									<h4>💰 ألوان المعلومات المالية</h4>
									<div class="color-items">
										<div class="color-item">
											<div class="color-preview">
												<div class="preview-text money-color-preview">$1,234</div>
											</div>
											<div class="color-controls">
												<label>💵 لون المحفظة</label>
												<div class="color-input-group">
													<input type="color" id="wallet-color-picker" value="#10b981">
													<input type="text" id="wallet-color-text" value="#10b981" placeholder="#10b981">
													<button class="color-reset-btn" data-target="wallet">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-text bank-color-preview">$50,000</div>
											</div>
											<div class="color-controls">
												<label>🏦 لون البنك</label>
												<div class="color-input-group">
													<input type="color" id="bank-color-picker" value="#3b82f6">
													<input type="text" id="bank-color-text" value="#3b82f6" placeholder="#3b82f6">
													<button class="color-reset-btn" data-target="bank">🔄</button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<!-- أدوات الألوان المتقدمة -->
							<div class="advanced-color-tools">
								<h4>🎨 أدوات الألوان المتقدمة</h4>
								<div class="color-tools-grid">
									<div class="color-tool">
										<label>🌈 مولد الألوان التلقائي</label>
										<div class="tool-controls">
											<select id="color-scheme-generator">
												<option value="complementary">ألوان متكاملة</option>
												<option value="analogous">ألوان متجاورة</option>
												<option value="triadic">ألوان ثلاثية</option>
												<option value="monochromatic">ألوان أحادية</option>
											</select>
											<button class="tool-btn" id="generate-colors">توليد</button>
										</div>
									</div>

									<div class="color-tool">
										<label>💾 حفظ مجموعة الألوان</label>
										<div class="tool-controls">
											<input type="text" id="color-preset-name" placeholder="اسم المجموعة">
											<button class="tool-btn" id="save-color-preset">حفظ</button>
										</div>
									</div>

									<div class="color-tool">
										<label>📋 استيراد/تصدير الألوان</label>
										<div class="tool-controls">
											<button class="tool-btn" id="export-colors">تصدير</button>
											<button class="tool-btn" id="import-colors">استيراد</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- تبويب الإعدادات المتقدمة -->
					<div class="tab-content" id="advanced-tab">
						<div class="panel-section">
							<h3>⚙️ الإعدادات المتقدمة</h3>
							<p>إعدادات متقدمة للمحترفين لتخصيص تجربة الـ HUD بالكامل</p>

							<div class="advanced-settings">
								<!-- إعدادات الأداء -->
								<div class="settings-category">
									<h4>⚡ إعدادات الأداء</h4>
									<div class="settings-list">
										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">🎭 تفعيل الانيميشن</span>
												<span class="setting-desc">تشغيل/إيقاف جميع التأثيرات المتحركة</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="animations-toggle" checked>
												<label for="animations-toggle"></label>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">🌫️ تأثيرات التمويه</span>
												<span class="setting-desc">تفعيل تأثيرات backdrop-filter</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="blur-effects-toggle" checked>
												<label for="blur-effects-toggle"></label>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">✨ تأثيرات الإضاءة</span>
												<span class="setting-desc">تشغيل تأثيرات الظلال والتوهج</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="glow-effects-toggle" checked>
												<label for="glow-effects-toggle"></label>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">🔄 معدل التحديث</span>
												<span class="setting-desc">سرعة تحديث عناصر الـ HUD</span>
											</div>
											<div class="range-control">
												<input type="range" id="update-rate" min="100" max="1000" value="500" step="100">
												<span class="range-value">500ms</span>
											</div>
										</div>
									</div>
								</div>

								<!-- إعدادات العرض -->
								<div class="settings-category">
									<h4>🖥️ إعدادات العرض</h4>
									<div class="settings-list">
										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">📏 حجم الـ HUD</span>
												<span class="setting-desc">تكبير أو تصغير جميع عناصر الـ HUD</span>
											</div>
											<div class="range-control">
												<input type="range" id="hud-scale" min="0.5" max="2" value="1" step="0.1">
												<span class="range-value">100%</span>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">🌙 الوضع الليلي</span>
												<span class="setting-desc">تفعيل الألوان الداكنة للعب الليلي</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="dark-mode-toggle">
												<label for="dark-mode-toggle"></label>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">👁️ الشفافية العامة</span>
												<span class="setting-desc">شفافية جميع عناصر الـ HUD</span>
											</div>
											<div class="range-control">
												<input type="range" id="global-opacity" min="0.1" max="1" value="1" step="0.1">
												<span class="range-value">100%</span>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">🎯 وضع التركيز</span>
												<span class="setting-desc">إخفاء العناصر غير الضرورية</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="focus-mode-toggle">
												<label for="focus-mode-toggle"></label>
											</div>
										</div>
									</div>
								</div>

								<!-- إعدادات التفاعل -->
								<div class="settings-category">
									<h4>🎮 إعدادات التفاعل</h4>
									<div class="settings-list">
										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">⌨️ اختصارات لوحة المفاتيح</span>
												<span class="setting-desc">تفعيل الاختصارات السريعة</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="keyboard-shortcuts-toggle" checked>
												<label for="keyboard-shortcuts-toggle"></label>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">🖱️ التحكم بالماوس</span>
												<span class="setting-desc">سحب وإفلات العناصر بالماوس</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="mouse-control-toggle" checked>
												<label for="mouse-control-toggle"></label>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">🔊 الأصوات</span>
												<span class="setting-desc">تشغيل أصوات التفاعل</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="sounds-toggle">
												<label for="sounds-toggle"></label>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">📳 الاهتزاز</span>
												<span class="setting-desc">اهتزاز عند التفاعل (للأجهزة المدعومة)</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="vibration-toggle">
												<label for="vibration-toggle"></label>
											</div>
										</div>
									</div>
								</div>

								<!-- إعدادات النظام -->
								<div class="settings-category">
									<h4>🔧 إعدادات النظام</h4>
									<div class="settings-list">
										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">💾 الحفظ التلقائي</span>
												<span class="setting-desc">حفظ الإعدادات تلقائياً عند التغيير</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="auto-save-toggle" checked>
												<label for="auto-save-toggle"></label>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">🔄 التحديث التلقائي</span>
												<span class="setting-desc">تحديث الـ HUD تلقائياً عند توفر إصدار جديد</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="auto-update-toggle" checked>
												<label for="auto-update-toggle"></label>
											</div>
										</div>

										<div class="setting-item">
											<div class="setting-info">
												<span class="setting-name">📊 إحصائيات الاستخدام</span>
												<span class="setting-desc">جمع إحصائيات مجهولة لتحسين الأداء</span>
											</div>
											<div class="toggle-switch">
												<input type="checkbox" id="analytics-toggle">
												<label for="analytics-toggle"></label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- تبويب القوالب الجاهزة -->
					<div class="tab-content" id="presets-tab">
						<div class="panel-section">
							<h3>📋 القوالب الجاهزة</h3>
							<p>قوالب محترفة جاهزة للاستخدام، اختر ما يناسب أسلوب لعبك</p>

							<div class="presets-container">
								<!-- القوالب الافتراضية -->
								<div class="presets-category">
									<h4>🎨 القوالب الافتراضية</h4>
									<div class="presets-grid">
										<div class="preset-card" data-preset="classic">
											<div class="preset-preview">
												<div class="preview-elements">
													<div class="preview-health" style="background: #cc3c2e;"></div>
													<div class="preview-armor" style="background: #3585da;"></div>
													<div class="preview-money" style="background: #10b981;">$1,234</div>
												</div>
											</div>
											<div class="preset-info">
												<h5>🏛️ الكلاسيكي</h5>
												<p>التصميم الأصلي مع ألوان تقليدية</p>
												<div class="preset-actions">
													<button class="preset-btn apply-btn" data-preset="classic">تطبيق</button>
													<button class="preset-btn preview-btn" data-preset="classic">معاينة</button>
												</div>
											</div>
										</div>

										<div class="preset-card" data-preset="modern">
											<div class="preset-preview">
												<div class="preview-elements">
													<div class="preview-health" style="background: linear-gradient(45deg, #ef4444, #dc2626);"></div>
													<div class="preview-armor" style="background: linear-gradient(45deg, #3b82f6, #1d4ed8);"></div>
													<div class="preview-money" style="background: linear-gradient(45deg, #10b981, #059669);">$1,234</div>
												</div>
											</div>
											<div class="preset-info">
												<h5>🚀 الحديث</h5>
												<p>تصميم عصري مع تدرجات لونية</p>
												<div class="preset-actions">
													<button class="preset-btn apply-btn" data-preset="modern">تطبيق</button>
													<button class="preset-btn preview-btn" data-preset="modern">معاينة</button>
												</div>
											</div>
										</div>

										<div class="preset-card" data-preset="neon">
											<div class="preset-preview">
												<div class="preview-elements">
													<div class="preview-health" style="background: #ff0080; box-shadow: 0 0 10px #ff0080;"></div>
													<div class="preview-armor" style="background: #00ffff; box-shadow: 0 0 10px #00ffff;"></div>
													<div class="preview-money" style="background: #ffff00; box-shadow: 0 0 10px #ffff00; color: #000;">$1,234</div>
												</div>
											</div>
											<div class="preset-info">
												<h5>💫 النيون</h5>
												<p>ألوان نيون مشعة للعب الليلي</p>
												<div class="preset-actions">
													<button class="preset-btn apply-btn" data-preset="neon">تطبيق</button>
													<button class="preset-btn preview-btn" data-preset="neon">معاينة</button>
												</div>
											</div>
										</div>

										<div class="preset-card" data-preset="minimal">
											<div class="preset-preview">
												<div class="preview-elements">
													<div class="preview-health" style="background: #6b7280;"></div>
													<div class="preview-armor" style="background: #6b7280;"></div>
													<div class="preview-money" style="background: #6b7280;">$1,234</div>
												</div>
											</div>
											<div class="preset-info">
												<h5>⚪ المبسط</h5>
												<p>تصميم بسيط بألوان محايدة</p>
												<div class="preset-actions">
													<button class="preset-btn apply-btn" data-preset="minimal">تطبيق</button>
													<button class="preset-btn preview-btn" data-preset="minimal">معاينة</button>
												</div>
											</div>
										</div>
									</div>
								</div>

								<!-- قوالب الألعاب -->
								<div class="presets-category">
									<h4>🎮 قوالب الألعاب</h4>
									<div class="presets-grid">
										<div class="preset-card" data-preset="racing">
											<div class="preset-preview">
												<div class="preview-elements">
													<div class="preview-health" style="background: #ff4500;"></div>
													<div class="preview-armor" style="background: #1e90ff;"></div>
													<div class="preview-money" style="background: #ffd700;">$1,234</div>
												</div>
											</div>
											<div class="preset-info">
												<h5>🏎️ السباق</h5>
												<p>ألوان سريعة ومثيرة للسباقات</p>
												<div class="preset-actions">
													<button class="preset-btn apply-btn" data-preset="racing">تطبيق</button>
													<button class="preset-btn preview-btn" data-preset="racing">معاينة</button>
												</div>
											</div>
										</div>

										<div class="preset-card" data-preset="military">
											<div class="preset-preview">
												<div class="preview-elements">
													<div class="preview-health" style="background: #8b4513;"></div>
													<div class="preview-armor" style="background: #556b2f;"></div>
													<div class="preview-money" style="background: #2f4f4f;">$1,234</div>
												</div>
											</div>
											<div class="preset-info">
												<h5>🪖 العسكري</h5>
												<p>ألوان عسكرية للألعاب التكتيكية</p>
												<div class="preset-actions">
													<button class="preset-btn apply-btn" data-preset="military">تطبيق</button>
													<button class="preset-btn preview-btn" data-preset="military">معاينة</button>
												</div>
											</div>
										</div>

										<div class="preset-card" data-preset="fantasy">
											<div class="preset-preview">
												<div class="preview-elements">
													<div class="preview-health" style="background: #dc143c;"></div>
													<div class="preview-armor" style="background: #4169e1;"></div>
													<div class="preview-money" style="background: #daa520;">$1,234</div>
												</div>
											</div>
											<div class="preset-info">
												<h5>🧙 الخيال</h5>
												<p>ألوان سحرية لألعاب الخيال</p>
												<div class="preset-actions">
													<button class="preset-btn apply-btn" data-preset="fantasy">تطبيق</button>
													<button class="preset-btn preview-btn" data-preset="fantasy">معاينة</button>
												</div>
											</div>
										</div>
									</div>
								</div>

								<!-- القوالب المخصصة -->
								<div class="presets-category">
									<h4>👤 القوالب المخصصة</h4>
									<div class="custom-presets">
										<div class="custom-preset-creator">
											<h5>➕ إنشاء قالب جديد</h5>
											<div class="creator-form">
												<input type="text" id="custom-preset-name" placeholder="اسم القالب">
												<textarea id="custom-preset-description" placeholder="وصف القالب"></textarea>
												<button class="preset-btn create-btn" id="create-custom-preset">إنشاء قالب</button>
											</div>
										</div>

										<div class="saved-presets" id="saved-presets-container">
											<!-- القوالب المحفوظة ستظهر هنا -->
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

					<!-- تبويب الألوان المتقدم -->
					<div class="tab-content" id="colors-tab">
						<div class="panel-section">
							<h3>🌈 تخصيص الألوان المتقدم</h3>
							<p>اختر الألوان المثالية لكل عنصر في الـ HUD مع معاينة فورية</p>

							<div class="color-categories">
								<!-- عناصر الصحة والحالة -->
								<div class="color-category">
									<h4>🩺 ألوان الصحة والحالة</h4>
									<div class="color-items">
										<div class="color-item">
											<div class="color-preview">
												<div class="preview-circle health-color-preview"></div>
											</div>
											<div class="color-controls">
												<label>❤️ لون الصحة</label>
												<div class="color-input-group">
													<input type="color" id="health-color-picker" value="#cc3c2e">
													<input type="text" id="health-color-text" value="#cc3c2e" placeholder="#cc3c2e">
													<button class="color-reset-btn" data-target="health">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-circle armor-color-preview"></div>
											</div>
											<div class="color-controls">
												<label>🛡️ لون الدرع</label>
												<div class="color-input-group">
													<input type="color" id="armor-color-picker" value="#3585da">
													<input type="text" id="armor-color-text" value="#3585da" placeholder="#3585da">
													<button class="color-reset-btn" data-target="armor">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-circle hunger-color-preview"></div>
											</div>
											<div class="color-controls">
												<label>🍔 لون الجوع</label>
												<div class="color-input-group">
													<input type="color" id="hunger-color-picker" value="#86fa01">
													<input type="text" id="hunger-color-text" value="#86fa01" placeholder="#86fa01">
													<button class="color-reset-btn" data-target="hunger">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-circle thirst-color-preview"></div>
											</div>
											<div class="color-controls">
												<label>💧 لون العطش</label>
												<div class="color-input-group">
													<input type="color" id="thirst-color-picker" value="#00b6e3">
													<input type="text" id="thirst-color-text" value="#00b6e3" placeholder="#00b6e3">
													<button class="color-reset-btn" data-target="thirst">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-circle stamina-color-preview"></div>
											</div>
											<div class="color-controls">
												<label>🏃 لون القدرة على التحمل</label>
												<div class="color-input-group">
													<input type="color" id="stamina-color-picker" value="#ffc100">
													<input type="text" id="stamina-color-text" value="#ffc100" placeholder="#ffc100">
													<button class="color-reset-btn" data-target="stamina">🔄</button>
												</div>
											</div>
										</div>
									</div>
								</div>

								<!-- المعلومات المالية والشخصية -->
								<div class="color-category">
									<h4>💰 ألوان المعلومات المالية</h4>
									<div class="color-items">
										<div class="color-item">
											<div class="color-preview">
												<div class="preview-text money-color-preview">$1,234</div>
											</div>
											<div class="color-controls">
												<label>💵 لون المحفظة</label>
												<div class="color-input-group">
													<input type="color" id="wallet-color-picker" value="#10b981">
													<input type="text" id="wallet-color-text" value="#10b981" placeholder="#10b981">
													<button class="color-reset-btn" data-target="wallet">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-text bank-color-preview">$50,000</div>
											</div>
											<div class="color-controls">
												<label>🏦 لون البنك</label>
												<div class="color-input-group">
													<input type="color" id="bank-color-picker" value="#3b82f6">
													<input type="text" id="bank-color-text" value="#3b82f6" placeholder="#3b82f6">
													<button class="color-reset-btn" data-target="bank">🔄</button>
												</div>
											</div>
										</div>

										<div class="color-item">
											<div class="color-preview">
												<div class="preview-text job-color-preview">Police</div>
											</div>
											<div class="color-controls">
												<label>💼 لون الوظيفة</label>
												<div class="color-input-group">
													<input type="color" id="job-color-picker" value="#f59e0b">
													<input type="text" id="job-color-text" value="#f59e0b" placeholder="#f59e0b">
													<button class="color-reset-btn" data-target="job">🔄</button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<!-- أدوات الألوان المتقدمة -->
							<div class="advanced-color-tools">
								<h4>🎨 أدوات الألوان المتقدمة</h4>
								<div class="color-tools-grid">
									<div class="color-tool">
										<label>🌈 مولد الألوان التلقائي</label>
										<div class="tool-controls">
											<select id="color-scheme-selector">
												<option value="default">الألوان الافتراضية</option>
												<option value="warm">الألوان الدافئة</option>
												<option value="cool">الألوان الباردة</option>
												<option value="neon">الألوان النيون</option>
												<option value="pastel">الألوان الباستيل</option>
												<option value="dark">الألوان الداكنة</option>
											</select>
											<button class="modern-button" id="apply-color-scheme">تطبيق</button>
										</div>
									</div>

									<div class="color-tool">
										<label>🎯 أداة انتقاء الألوان من الشاشة</label>
										<div class="tool-controls">
											<button class="modern-button" id="eyedropper-tool">🎯 انتقاء لون</button>
											<span class="tool-desc">انقر لاختيار لون من أي مكان على الشاشة</span>
										</div>
									</div>

									<div class="color-tool">
										<label>📋 حفظ ومشاركة الألوان</label>
										<div class="tool-controls">
											<button class="modern-button" id="export-colors">📤 تصدير الألوان</button>
											<button class="modern-button" id="import-colors">📥 استيراد الألوان</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

				<!-- قسم عناصر الصحة والحالة -->
				<div class="panel-section">
					<h3>🩺 عناصر الصحة والحالة</h3>
					<p>تحكم في عرض عناصر الصحة والدرع والجوع والعطش والقدرة على التحمل</p>
					<div class="toggle-grid">
						<div class="toggle-button-cover">
							<span class="toggle-label">❤️ الصحة</span>
							<img src="./img/hud/icon_Heartbeat.png">
							<div class="button r" id="button-4">
								<input class="checkbox button" data-for="health" type="checkbox">
								<div class="knobs"></div>
								<div class="layer"></div>
							</div>
						</div>
						<div class="toggle-button-cover">
							<span class="toggle-label">🛡️ الدرع</span>
							<img src="./img/hud/icon_Alternate_Shield.png">
							<div class="button r" id="button-4">
								<input class="checkbox button" data-for="armor" type="checkbox">
								<div class="knobs"></div>
								<div class="layer"></div>
							</div>
						</div>
						<div class="toggle-button-cover">
							<span class="toggle-label">🍔 الجوع</span>
							<img src="./img/hud/icon_Hamburger.png">
							<div class="button r" id="button-4">
								<input class="checkbox button" data-for="hunger" type="checkbox">
								<div class="knobs"></div>
								<div class="layer"></div>
							</div>
						</div>
						<div class="toggle-button-cover">
							<span class="toggle-label">💧 العطش</span>
							<img src="./img/hud/water.png">
							<div class="button r" id="button-4">
								<input class="checkbox button" data-for="thirst" type="checkbox">
								<div class="knobs"></div>
								<div class="layer"></div>
							</div>
						</div>
						<div class="toggle-button-cover">
							<span class="toggle-label">🏃 القدرة على التحمل</span>
							<img src="./img/hud/icon_Running.png">
							<div class="button r" id="button-4">
								<input class="checkbox button" data-for="stamina" type="checkbox">
								<div class="knobs"></div>
								<div class="layer"></div>
							</div>
						</div>
					</div>
				</div>

				<!-- قسم المعلومات المالية -->
				<div class="panel-section">
					<h3>💰 المعلومات المالية والشخصية</h3>
					<p>تحكم في عرض معلومات المال والبنك والوظيفة والهوية</p>
					<div class="toggle-grid">
						<div class="toggle-button-cover">
							<span class="toggle-label">💵 المحفظة</span>
							<img src="./img/hud/wallet.png">
							<div class="button r" id="button-4">
								<input class="checkbox button" data-for="wallet" type="checkbox">
								<div class="knobs"></div>
								<div class="layer"></div>
							</div>
						</div>
						<div class="toggle-button-cover">
							<span class="toggle-label">🏦 البنك</span>
							<img src="./img/hud/bank.png">
							<div class="button r" id="button-4">
								<input class="checkbox button" data-for="Bank" type="checkbox">
								<div class="knobs"></div>
							<div class=layer></div>
						</div>
					</div>
                    <div class=toggle-button-cover> 
                        <img src=./img/hud/job.png class=white-icon style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=Job type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
                    <div class=toggle-button-cover> 
                        <img src=./img/hud/icon_Identification_Card.png  style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=id type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
                                        <div class=toggle-button-cover> 
                        <img src=./img/hud/icon_Copyright.png  style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=id type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
			</div>
				<div class=row >
                    <div class=toggle-button-cover> 
                        <img src=./img/hud/cinematic1.png style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=serverlogo type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
					<div class=toggle-button-cover> 
                        <img src=./img/hud/map1.png style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=serverlogo type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
					<div class=toggle-button-cover> 
                        <img src=./img/hud/Transport-Speedometer1.png style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=count type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
					<div class=toggle-button-cover> 
                        <img src=./img/hud/time.png class=white-icon style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=time type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
					<div class=toggle-button-cover> 
                        <img src=./img/hud/icon_Clock.png style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=postals type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
				</div>
				<div class=row >
					<div class=toggle-button-cover> 
                        <img src=./img/hud/icon_location-arrow.png style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=seatbelt type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
					<div class=toggle-button-cover> 
                        <img src=./img/hud/icon_Globe2.png style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=lights type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
					<div class=toggle-button-cover> 
                        <img src=./img/hud/keybinds.png class=white-icon style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=jobs type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
					<div class=toggle-button-cover> 
                        <img src=./img/hud/logo.png class=white-icon style="top:15px;">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=keybinds type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
                    <div class=toggle-button-cover> 
                        <img src=./img/hud/user-avatar.png style="top:15px; border-radius:50px">
						<div class="button r" id=button-4> 
                            <input class="checkbox button" data-for=keybinds type=checkbox>
							<div class=knobs></div>
							<div class=layer></div>
						</div>
					</div>
				</div>
				</div>

				<!-- قسم تخصيص الألوان -->
				<div class="color-picker-section">
					<h3>🎨 تخصيص الألوان</h3>
					<p>اختر العنصر الذي تريد تغيير لونه، ثم استخدم أداة اختيار الألوان لتخصيص اللون المفضل</p>

					<div class="color-selection-container">
						<label for="selection">🎯 اختر العنصر:</label>
						<select id="selection" class="modern-select">
							<option value="none" selected>🔽 اختر عنصر لتغيير لونه...</option>

							<!-- عناصر التحكم الأساسية -->
							<optgroup label="⚙️ عناصر التحكم">
								<option value="panl" data-type="panl">🎛️ لوحة التحكم</option>
							</optgroup>

							<!-- عناصر الصحة والحالة -->
							<optgroup label="🩺 الصحة والحالة">
								<option value="health" data-type="stroke">❤️ الصحة</option>
								<option value="armor" data-type="stroke">🛡️ الدرع</option>
								<option value="hunger" data-type="stroke">🍔 الجوع</option>
								<option value="thirst" data-type="stroke">💧 العطش</option>
								<option value="stamina" data-type="stroke">🏃 القدرة على التحمل</option>
							</optgroup>

							<!-- معلومات اللاعب -->
							<optgroup label="👤 معلومات اللاعب">
								<option value="id" data-type="info-color">🆔 الهوية</option>
								<option value="wallet" data-type="info-color">💵 المحفظة</option>
								<option value="Bank" data-type="info-color">🏦 البنك</option>
								<option value="dirty" data-type="info-color">💸 الأموال القذرة</option>
								<option value="Job" data-type="info-color">💼 الوظيفة</option>
							</optgroup>

							<!-- عناصر المركبة -->
							<optgroup label="🚗 عناصر المركبة">
								<option value="speed-color" data-type="color">🏎️ عداد السرعة</option>
								<option value="fuel" data-type="stroke">⛽ البترول</option>
								<option value="seatbelt" data-type="stroke">🔒 حزام الأمان</option>
								<option value="lights" data-type="stroke">💡 الأضواء</option>
							</optgroup>

							<!-- عناصر الخريطة والموقع -->
							<optgroup label="🗺️ الخريطة والموقع">
								<option value="mapborder" data-type="border-color">🗺️ حدود الخريطة</option>
								<option value="nearest-postal" data-type="color">📍 أقرب مربع</option>
							</optgroup>

							<!-- عناصر الوقت والمعلومات -->
							<optgroup label="⏰ الوقت والمعلومات">
								<option value="TimeColor" data-type="watermark">🕐 الوقت</option>
								<option value="OnlineNumber" data-type="watermark">👥 اللاعبين المتصلين</option>
							</optgroup>

							<!-- عناصر إضافية -->
							<optgroup label="✨ عناصر إضافية">
								<option value="watermark" data-type="color">🌙 العلامة المائية</option>
								<option value="keybinds" data-type="color">⌨️ اختصارات لوحة المفاتيح</option>
								<option value="Weapon" data-type="color">🔫 السلاح</option>
							</optgroup>
						</select>

						<div class="color-picker-container">
							<label for="color-block">🎨 اختر اللون:</label>
							<input class="color-block"
								   data-wcp-autoresize="true"
								   data-wcp-cssclass="color-block"
								   data-wcp-format="rgba"
								   data-wcp-layout="block"
								   data-wcp-sliders="wsvap"
								   data-wheelcolorpicker=""
								   id="color-block"
								   value="#6366f1">
						</div>
					</div>
				</div>
			</div>
		</div>
        </div>
    </div>`)
    $(".pop11, .pop13, .pop14, .pop15, .pop16").hide()
    $(".k3, .k4, .k5, .k6, .k7, .k2").hide()

	var audio = null;
    

	const sleep = async (ms) => {
		try {
			return await new Promise(resolve => setTimeout(resolve, ms));
		} catch (error) {
			console.error("Error in sleep function:", error);
			return Promise.resolve();
		}
	};



	function GetCurrentTime() {
		try {
			var now = new Date();
			var hours = now.getHours();
			var minutes = now.getMinutes();
			var ampm = hours >= 12 ? " PM" : " AM";

			// Convert to 12-hour format
			hours = hours % 12;
			hours = hours || 12; // 0 should be 12

			// Add leading zero to minutes if needed
			minutes = minutes < 10 ? "0" + minutes : minutes;

			var timeString = Time + hours + ":" + minutes;
			var typeString = TimeType + ampm;

			return [timeString, typeString];
		} catch (error) {
			console.error("Error getting current time:", error);
			return [Time + "12:00", TimeType + " PM"];
		}
	}

	function StopDragging(event, ui) {
		try {
			var elementId = $(event.target).attr("id");
			if (!elementId) {
				console.warn("No element ID found for dragging");
				return;
			}

			var position = {
				left: ui.position.left,
				top: ui.position.top
			};

			localStorage.setItem(elementId + "-position", JSON.stringify(position));
			console.log("Position saved for element:", elementId, position);
		} catch (error) {
			console.error("Error saving drag position:", error);
		}
	}

    OnlineNumber = "";

$(function() {
    // إزالة القائمة من العناصر القابلة للسحب
    $("#watermarkx, #time, #count, #serverlogo, #Job, #Bank, #wallet, #dirty, #jobs, #Weapon, #keybinds, #map, #hunger1, #speedometer, #fuel, #seatbelt, #lights, #postals, #health, #armor, #hunger, #thirst, #stamina, #weapon, #id").draggable({
        containment: "window",
        stop: StopDragging,
        scroll: false
    });

    // منع حركة القائمة نهائياً
    try {
        $("#Panl").draggable("destroy");
    } catch(e) {}

    // منع جميع أحداث السحب على القائمة
    $("#Panl").off('mousedown touchstart dragstart drag dragend');

    // منع السحب من الهيدر
    $("#Panl .header").off('mousedown touchstart dragstart drag dragend');

    // إضافة مستمعات لمنع السحب
    document.getElementById('Panl')?.addEventListener('dragstart', function(e) {
        e.preventDefault();
        return false;
    });

    document.getElementById('Panl')?.addEventListener('mousedown', function(e) {
        // السماح بالنقر على الأزرار والعناصر التفاعلية فقط
        if (!e.target.closest('button, input, select, .tab-btn, .quick-btn, .options-button, #close')) {
            e.preventDefault();
        }
    });

    console.log("🔒 Panel movement disabled");

    // إصلاح مشاكل الأشكال والعرض
    fixDisplayIssues();

    // إصلاح مشكلة الخريطة
    fixMapDisplay();

    window.addEventListener("message", function(t) {
        var e = t.data;
        
        if (e.Type === "LSClear") {
            localStorage.clear();
        } else if (e.Type === "Restart") {
            location.reload();
        } else if (e.Type === "Share") {
            for (let a of Object.keys(e.data)) {
                if (a.includes("-position")) {
                    let o = JSON.parse(e.data[a]);
                    localStorage.setItem(a, JSON.stringify({ left: o.left, top: o.top }));
                }
                
                if (a.includes("-color")) {
                    let s = JSON.parse(e.data[a]);
                    localStorage.setItem(a, JSON.stringify({ type: s.type, color: s.color }));
                }
                
                if (a.includes("-Status")) {
                    let r = JSON.parse(e.data[a]);
                    localStorage.setItem(a, JSON.stringify({ Status: r.Status }));
                }
            }
        }

        if (e.Type === "OpenPanl") {
            $(".Panl").fadeIn(0);
        } else if (e.Type === "ClosePanl") {
            $(".Panl").fadeOut(0);
        } else if (e.Type === "serverlogo") {
            $("#serverlogo img").attr("src", e.logo);
        } else if (e.Type === "Time") {
            Time = e.Time;
            TimeType = e.TimeType;
        } else if (e.Type === "watermark") {
            Array.from(e.watermark).forEach(t => {
                config.messages.push(t);
            });
        } else if (e.Type === "Kay") {
            $("#keybinds").append(`<div class="HotKay"><div class="Kay"><span>${e.kay}</span></div><span>${e.text}</span></div>`);        
            $("#" + e.name + " span").html(e.Number + " -");
            $("#" + e.name + " i").html(e.icon);
        } else if (e.Type === "update") {
            $("#Job span").html(e.job);
            $("#Bank span").html(e.bankMoney);
            $("#wallet span").html(e.money);
            $("#dirty span").html(e.blackMoney);
            $("#server-name").html(e.servername);
            $("#iddd").html(e.codee);
            $("#discord51").html(e.discord);
            
            $("#id span").text(e.id);
            $("#count").html(e.online);
            if (OnlineNumber !== "") {
                $("#OnlineNumber").css("color", OnlineNumber);
            }
            progressCircle(e.Health, ".health");
            progressCircle(e.Health, ".hunger1");
            progressCircle(e.hunger, ".hunger");
            progressCircle(e.thirst, ".thirst");
            progressCircle(e.Stamina, ".stamina");
            progressCircle(e.Armour, ".armor");
        } else if (e.Type === "NearestPostal") {
            $(".nearest-postal").html(e.zone);
            $("#nearest-postal").html(e.street);
        } else if (e.Type === "InVehicle") {
            $(".carStats").fadeIn(0);
            Night.Vehicle = true;
        } else if (e.Type === "OutVehicle") {
            $(".carStats").fadeOut(0);
            Night.Vehicle = false;
        } else if (e.Type === "OpenCar") {
            if (e.Speed < 180) {
                setProgressSpeed(e.Speed, ".progress-speed");
            }
            progressCircle(e.Fuel, ".fuel");
            $(".speed").text(e.Speed);
        } else if (e.Type === "CloseHud") {
            $("#Hud").fadeOut(0);
        } else if (e.Type === "OpenHud") {
            $("#Hud").fadeIn(0);
        } else if (e.Type === "seatbeltOff") {
            $(".seatbelt").css("stroke", "red");
            $(".seatbelt-img").css("filter", "invert(25%) sepia(10000%) hue-rotate(0deg)");
        } else if (e.Type === "seatbeltOn") {
            $(".seatbelt").css("stroke", "green");
            $(".seatbelt-img").css("filter", "invert(100%) sepia(9000%) hue-rotate(50deg)");
        } else if (e.Type === "LightsOff") {
            $(".lights").css("stroke", "red");
            $(".lights-img").css("filter", "invert(25%) sepia(10000%) hue-rotate(0deg)");
            $(".lights-img").attr("href", "img/hud/lowbeamBlack.png");
        } else if (e.Type === "LightsOn") {
            $(".lights").css("stroke", "rgb(0, 204, 255)");
            $(".lights-img").css("filter", "invert(45%) sepia(60%) saturate(2083%) hue-rotate(162deg) brightness(95%) contrast(102%)");
            $(".lights-img").attr("href", "img/hud/car-light.png");
        }

        if (typeof Night.cinematic === 'undefined' || Night.cinematic === null) {
            Night.cinematic = false;
        }

        // تحديث صورة المستخدم عند وجود البيانات
        if (e.logo && document.getElementById('user-avatar')) {
            document.getElementById('user-avatar').src = e.logo;
        }

        if (!Night.cinematic) {
            if (typeof Night.Weapon === 'undefined' || Night.Weapon === null) {
                Night.Weapon = false;
            }

            if (!Night.Weapon && e.Type === "Weapon") {
                $(".Weapon").fadeIn(0);
                $("#Weapon .img img").attr("src", "./img/weapons/" + e.Weapon + ".png");
                $("#Weapon .ammo span").html(e.ammo);
            } else if (e.Type === "displayWeapon") {
                $(".Weapon").fadeOut(0);
            }
        }
    });

    length();
});

	function length() {
		try {
			for (let t = 0; t < localStorage.length; t++) {
				let key = localStorage.key(t);
				if (!key) continue;

				let e = localStorage.getItem(key);
				if (!e) continue;

				let a;
				try {
					a = JSON.parse(e);
				} catch (parseError) {
					console.warn("Failed to parse localStorage item:", key, parseError);
					continue;
				}

                if (key.includes("-position")) {
                    let Name = key.split("-")[0];
                    $("#" + Name).css("left", a.left + "px");
                    $("#" + Name).css("top", a.top + "px");
                }

                if (key.includes("-color")) {
                    let Name = key.split("-")[0];

                    if ("panl" === a.type) {
                        $(".header").css("border-bottom", "1px solid " + a.color);
                        $(".Panl").css("border", "1px solid " + a.color);

                    } else if ("color" === a.type) {
                        $("#" + Name).css("color", a.color);

                        // Special handling for specific elements
                        if (Name === "speed-color") {
                            $(".speed").css("color", a.color);
                            $(".mph").css("color", a.color);
                        } else if (Name === "nearest-postal") {
                            $("#postals").css("color", a.color);
                        } else if (Name === "watermark") {
                            $("#watermark").css("color", a.color);
                            $("#watermarkx").css("color", a.color);
                        } else if (Name === "keybinds") {
                            $("#keybinds").css("color", a.color);
                            $(".HotKay").css("color", a.color);
                        } else if (Name === "Weapon") {
                            $("#Weapon").css("color", a.color);
                            $("#Weapon span").css("color", a.color);
                        }

                    } else if ("info-color" === a.type) {
                        $("#" + Name + " span").css("color", a.color);
                        $("#" + Name + " i").css("color", a.color);

                    } else if ("stroke" === a.type) {
                        $("#" + Name + " circle").css("stroke", a.color);
                        $("#" + Name + "-circle").css("stroke", a.color);
                        $("." + Name + " circle").css("stroke", a.color);

                        // Special handling for vehicle elements
                        if (Name === "fuel") {
                            $("#fuel-circle").css("stroke", a.color);
                        } else if (Name === "seatbelt") {
                            $("#seatbelt-circle").css("stroke", a.color);
                        } else if (Name === "lights") {
                            $("#lights-circle").css("stroke", a.color);
                        }

                    } else if ("border-color" === a.type) {
                        $("#" + Name).css("border-color", a.color);

                    } else if ("watermark" === a.type) {
                        if ("TimeColor" === Name) {
                            TimeColor = a.color;
                            $("#TimeColor").css("color", a.color);
                        } else if ("OnlineNumber" === Name) {
                            OnlineNumber = a.color;
                            $("#OnlineNumber").css("color", a.color);
                        }
                    }
                }

                if (key.includes("-Status")) {
                    let Name = key.split("-")[0];
                    if ("cinematic" !== Name) {
                        if (a.Status) {
                            Black[Name] = false;
                            $("#" + Name).fadeIn(0);
                            $('input[data-for="' + Name + '"]').prop("checked", false);
                        } else {
                            Black[Name] = true;
                            $("#" + Name).fadeOut(0);
                            $('input[data-for="' + Name + '"]').prop("checked", true);
                        }
                        if ("map" === Name) {
                            $.post("https://Night-Hud/StatusMap", JSON.stringify({ Status: a.Status }));
                        }
                    }
                }
			}
		} catch (error) {
			console.error("Error in length function:", error);
		}
	}

    // Time and color variables
    var Time = "<font id='TimeColor' color='red'>";
    var TimeColor = "";
    var TimeType = "<font id='TimeType' color='white'>";
        (async () => {
			try {
				while (true) {
					await sleep(100);
					try {
						$("#time").html(GetCurrentTime());
						if (TimeColor && TimeColor !== "") {
							$("#TimeColor").css("color", TimeColor);
						}
					} catch (innerError) {
						console.warn("Error updating time display:", innerError);
					}
				}
			} catch (error) {
				console.error("Error in time update loop:", error);
			}
		})();
    var config = {
       messages: []
    };

    function progressCircle(percentage, selector) {
        try {
            let element = document.querySelector(selector);
            if (!element) {
                console.warn("Progress circle element not found:", selector);
                return;
            }

            if (!element.r || !element.r.baseVal) {
                console.warn("Invalid circle element:", selector);
                return;
            }

            let radius = element.r.baseVal.value;
            let circumference = 2 * radius * Math.PI;
            let progressText = $(selector).parent().parent().find(".progress-text");

            element.style.strokeDasharray = circumference + " " + circumference;
            element.style.strokeDashoffset = -(circumference - (percentage / 100) * circumference);

            if (progressText.length > 0) {
                progressText.text(Math.round(percentage));
            }

            console.log("Progress circle updated:", selector, percentage + "%");
        } catch (error) {
            console.error("Error updating progress circle:", selector, error);
        }
    }

	function setProgressSpeed(t, e) {
		var a = document.querySelector(e),
			o = 2 * a.r.baseVal.value * Math.PI,
			s = $(e).parent().parent().find("");
            a.style.strokeDasharray = o + " " + o;
            a.style.strokeDashoffset = "" + o;
            a.style.strokeDashoffset = -(o - -(73 * (100 * t / 220)) / 100 / 100 * o);		
            var r = Math.floor(3.6 * t);
		(99 == r || 139 == r) && (r -= 1), s.text(r)
	}

	function playSound(t) {
		(audio = new Audio(t)).play(), audio.volume = .09
	}
    $(function() {
    var t = 0;
    setInterval(() => {
        $("#watermarkx").fadeOut("slow", function() {
            $(this).html(config.messages[t]);
            $(this).fadeIn("slow");
        });
        (t += 1) >= config.messages.length && (t = 0);
    }, 3000);
});

$("#close").on("click", function() {
    $.post("https://Night-Hud/Close", JSON.stringify({}));
});
$("#close1").on("click", function() {
    $.post("https://Night-Hud/Close", JSON.stringify({}));
});

$(".row-content").on("click", ".checkbox", function() {
    var t = $(this).attr("data-for");

    if (t === "warning") {
        if (typeof Black[t] === 'undefined' || Black[t] === null) Black[t] = false;

        if (Black[t]) {
            Black[t] = false;
            $("#" + t).fadeOut(0);
            $(".button").prop("disabled", false).css("cursor", "pointer");
            playSound("https://cdn.discordapp.com/attachments/1075006669953712198/1080482763477631146/correct-choice-43861.mp3");
            localStorage.setItem(t + "-Status", JSON.stringify({ Status: false }));
            $.post("https://Night-Hud/StatusMap", JSON.stringify({ Status: true }));

            if (Night.AnotherFeature) $(".warningStats").fadeIn();
            $(".warn").fadeIn();
            length();
        } else {
            Black[t] = true;
            $(".warn").fadeOut();
            $(".warningStats").fadeOut();
            $(".button").prop("disabled", true).css("cursor", "not-allowed");
            playSound("https://cdn.discordapp.com/attachments/1075006669953712198/1080482763477631148/interface-124464.mp3");
            localStorage.setItem(t + "-Status", JSON.stringify({ Status: true }));
            $.post("https://Night-Hud/StatusMap", JSON.stringify({ Status: false }));

            setTimeout(() => { $("#" + t).fadeIn(0); }, 500);
        }
        return;
    }

    if (Black[t]) {
        Black[t] = false;
        $("#" + t).fadeIn();
        playSound("https://cdn.discordapp.com/attachments/1075006669953712198/1080482763477631148/interface-124464.mp3");
        localStorage.setItem(t + "-Status", JSON.stringify({ Status: true }));

        if (t === "map") {
            $.post("https://Night-Hud/StatusMap", JSON.stringify({ Status: true }));
        }
    } else {
        Black[t] = true;
        $("#" + t).fadeOut();
        playSound("https://cdn.discordapp.com/attachments/1075006669953712198/1080482763477631146/correct-choice-43861.mp3");
        localStorage.setItem(t + "-Status", JSON.stringify({ Status: false }));

        if (t === "map") {
            $.post("https://Night-Hud/StatusMap", JSON.stringify({ Status: false }));
        }
    }
});

$("#selection").change(function() {
    try {
        var t = $(this).val(),
            e = $("#selection option:selected").attr("data-type");

        if (typeof Night === 'undefined') {
            Night = {};
        }

        Night.value = t;
        Night.datatype = e;

        console.log("Selection changed:", t, "Type:", e);
    } catch (error) {
        console.error("Error in selection change:", error);
    }
});

$("#color-block").on("colorchange", function() {
    try {
        var color = $(this).wheelColorPicker("value");

        if (typeof Night === 'undefined' || !Night.value || !Night.datatype) {
            console.warn("Night object or selection not properly initialized");
            return;
        }

        console.log("Color changed:", color, "for element:", Night.value, "type:", Night.datatype);

        if (Night.datatype === "stroke") {
        // Handle circular progress elements (health, armor, hunger, thirst, stamina, fuel, seatbelt, lights)
        $("#" + Night.value + " circle").css("stroke", color);
        $("#" + Night.value + "-circle").css("stroke", color);
        $("." + Night.value + " circle").css("stroke", color);

        // Special handling for fuel, seatbelt, lights
        if (Night.value === "fuel") {
            $("#fuel-circle").css("stroke", color);
        } else if (Night.value === "seatbelt") {
            $("#seatbelt-circle").css("stroke", color);
        } else if (Night.value === "lights") {
            $("#lights-circle").css("stroke", color);
        }

    } else if (Night.datatype === "info-color") {
        // Handle info elements (wallet, bank, job, id, dirty money)
        $("#" + Night.value + " span").css("color", color);
        $("#" + Night.value + " i").css("color", color);

    } else if (Night.datatype === "color") {
        // Handle text color elements
        $("#" + Night.value).css("color", color);

        // Special handling for specific elements
        if (Night.value === "speed-color") {
            $(".speed").css("color", color);
            $(".mph").css("color", color);
        } else if (Night.value === "nearest-postal") {
            $("#postals").css("color", color);
        } else if (Night.value === "watermark") {
            $("#watermark").css("color", color);
            $("#watermarkx").css("color", color);
        } else if (Night.value === "keybinds") {
            $("#keybinds").css("color", color);
            $(".HotKay").css("color", color);
        } else if (Night.value === "Weapon") {
            $("#Weapon").css("color", color);
            $("#Weapon span").css("color", color);
        }

    } else if (Night.datatype === "border-color") {
        // Handle border color elements
        $("#" + Night.value).css("border-color", color);

    } else if (Night.datatype === "panl") {
        // Handle panel styling
        $(".header").css("border-bottom", "1px solid " + color);
        $(".Panl").css("border", "1px solid " + color);

    } else if (Night.datatype === "watermark") {
        // Handle watermark elements that need special handling
        if (Night.value === "OnlineNumber") {
            OnlineNumber = color;
            $("#OnlineNumber").css("color", color);
        } else if (Night.value === "TimeColor") {
            TimeColor = color;
            $("#TimeColor").css("color", color);
        }
    }

        // Save the color setting to localStorage
        localStorage.setItem(Night.value + "-color", JSON.stringify({
            type: Night.datatype,
            color: color
        }));

        console.log("Color setting saved for:", Night.value);

    } catch (error) {
        console.error("Error in color change handler:", error);
    }
});
document.addEventListener("DOMContentLoaded", function () {
    const buttonSkl = document.getElementById("button-skl1");
        buttonSkl.addEventListener("change", function () {
            $(".pop11, .pop12, .pop13, .pop14, .pop15, .pop16, .pop17").hide();
             const selectedValue = buttonSkl.value;
              if (selectedValue !== "none") {
                $("." + selectedValue).show();
              }
            });
        });
$(document).ready(function() {
    $('#button-Skl2').change(function() {
        $(".k1, .k2, .k3, .k4, .k5, .k6, .k7").hide();
          const selectedValue = $(this).val();
            if (selectedValue) {
                $("." + selectedValue).show();
        }
    });
});
let Share = {};
let isProcessing = false;
$("#button-Share").on("click", function() {
    $.post("https://Night-Hud/Share", JSON.stringify({ Share: Share }));
    $.post("https://Night-Hud/Close", JSON.stringify({}));
});
$("#button-position").on("click", function() {
    let t = localStorage.length;
    for (let a = 0; a < t; a++) {
        var o = localStorage.key(a);
        if (o?.includes("-position")) {
            a--;
            localStorage.removeItem(o);
            setTimeout(() => {
                location.reload();
            }, 100);
        }
    }
    $.post("https://Night-Hud/Close", JSON.stringify({}));
});

$("#button-color").on("click", function() {
    try {
        console.log("Resetting all colors...");
        let keysToRemove = [];

        // Collect all color keys first to avoid issues with changing localStorage during iteration
        for (let i = 0; i < localStorage.length; i++) {
            let key = localStorage.key(i);
            if (key && key.includes("-color")) {
                keysToRemove.push(key);
            }
        }

        // Remove all color keys
        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
            console.log("Removed color setting:", key);
        });

        console.log("Color reset complete, reloading...");
        setTimeout(() => {
            location.reload();
        }, 100);

        $.post("https://Night-Hud/Close", JSON.stringify({}));
    } catch (error) {
        console.error("Error resetting colors:", error);
    }
});
document.getElementById('map-toggle-btn').addEventListener('click', function() {
    try {
        const map = document.getElementById('mapborder');
        if (!map) {
            console.error("Map element not found");
            return;
        }

        const isSquare = map.classList.contains('circle');
        console.log("Toggling map shape, current is circle:", isSquare);

        if (isSquare) {
            map.classList.remove('circle');
            map.classList.add('square');
            map.style.borderRadius = '50%';
            this.textContent = 'الشكل المربع';
            sendMapTypeToServer('circle');
            console.log("Map changed to square");
        } else {
            map.classList.remove('square');
            map.classList.add('circle');
            map.style.borderRadius = '15px';
            this.textContent = 'الشكل الدائري';
            sendMapTypeToServer('square');
            console.log("Map changed to circle");
        }
    } catch (error) {
        console.error("Error toggling map shape:", error);
    }
});
function sendMapTypeToServer(type) {
    try {
        console.log("Sending map type to server:", type);
        fetch('https://Night-Hud/changeMapType', {
            method: 'POST',
            body: JSON.stringify({ mapType: type }),
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(response => {
            if (response.ok) {
                console.log("Map type sent successfully");
            } else {
                console.error("Failed to send map type:", response.status);
            }
        }).catch(error => {
            console.error("Error sending map type:", error);
        });
    } catch (error) {
        console.error("Error in sendMapTypeToServer:", error);
    }
}


var totalSeconds = 0;
var timerVar = setInterval(countTimer, 1000);

function countTimer() {
    ++totalSeconds;
    var hour = Math.floor(totalSeconds / 3600);
    var minute = Math.floor((totalSeconds - hour * 3600) / 60);
    if (hour < 10) hour = "0" + hour;
    if (minute < 10) minute = "0" + minute;

    document.getElementById("timeee").innerHTML = hour + "h " + minute + "m";
}

// Initialize color picker when document is ready
$(document).ready(function() {
    console.log("🚀 Document ready, initializing Night HUD...");

    // Initialize Night object if not exists
    if (typeof window.Night === 'undefined') {
        window.Night = {};
        console.log("Night object initialized");
    }

    // Initialize the wheelColorPicker
    try {
        $("#color-block").wheelColorPicker({
            format: 'rgba',
            autoResize: true,
            cssClass: 'color-block',
            layout: 'block',
            sliders: 'wsvap'
        });
        console.log("✅ Color picker initialization attempted");
    } catch (error) {
        console.error("❌ Error initializing color picker:", error);
    }

    // Make sure the color picker is properly initialized
    setTimeout(function() {
        if ($("#color-block").data('wheelColorPicker')) {
            console.log("✅ Color picker initialized successfully");
        } else {
            console.log("❌ Color picker failed to initialize, retrying...");
            try {
                $("#color-block").wheelColorPicker({
                    format: 'rgba',
                    autoResize: true,
                    cssClass: 'color-block',
                    layout: 'block',
                    sliders: 'wsvap'
                });

                // Check again after retry
                setTimeout(function() {
                    if ($("#color-block").data('wheelColorPicker')) {
                        console.log("✅ Color picker initialized successfully on retry");
                    } else {
                        console.error("❌ Color picker failed to initialize even after retry");
                        console.log("Available jQuery plugins:", Object.keys($.fn));
                    }
                }, 500);
            } catch (error) {
                console.error("Error on color picker retry:", error);
            }
        }
    }, 1000);

    // Test color selection functionality
    $("#selection").on('change', function() {
        try {
            var selectedValue = $(this).val();
            var selectedType = $("#selection option:selected").attr("data-type");
            console.log("🎨 Color selection changed:", selectedValue, "Type:", selectedType);
        } catch (error) {
            console.error("❌ Error in color selection test:", error);
        }
    });

    // Add global error handler
    window.addEventListener('error', function(e) {
        console.error('🚨 Global error caught:', e.error);
        console.error('File:', e.filename, 'Line:', e.lineno, 'Column:', e.colno);
    });

    // Add unhandled promise rejection handler
    window.addEventListener('unhandledrejection', function(e) {
        console.error('🚨 Unhandled promise rejection:', e.reason);
    });

    console.log("🎉 Night HUD initialization complete!");

    // Initialize tab system
    initTabSystem();

    // Initialize quick actions
    initQuickActions();

    // Initialize advanced systems
    initAdvancedColorSystem();
    initAdvancedSettings();
    initPresetsSystem();

    // إصلاح مشاكل الأشكال والعرض
    fixDisplayIssues();

    // إصلاح مشكلة الخريطة
    setTimeout(fixMapDisplay, 1000);
});

// Tab System Functions
function initTabSystem() {
    try {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');

                // Remove active class from all tabs and contents
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding content
                const targetContent = document.getElementById(targetTab + '-tab');
                if (targetContent) {
                    targetContent.classList.add('active');
                }

                console.log("🔄 Switched to tab:", targetTab);
            });
        });

        console.log("✅ Tab system initialized");
    } catch (error) {
        console.error("❌ Error initializing tab system:", error);
    }
}

// Quick Actions Functions
function initQuickActions() {
    try {
        // Quick Save
        document.getElementById('quick-save')?.addEventListener('click', function() {
            try {
                const settings = gatherAllSettings();
                localStorage.setItem('night-hud-quick-save', JSON.stringify(settings));
                showNotification('💾 تم الحفظ السريع بنجاح!', 'success');
                console.log("💾 Quick save completed");
            } catch (error) {
                showNotification('❌ فشل في الحفظ السريع', 'error');
                console.error("Error in quick save:", error);
            }
        });

        // Quick Load
        document.getElementById('quick-load')?.addEventListener('click', function() {
            try {
                const savedSettings = localStorage.getItem('night-hud-quick-save');
                if (savedSettings) {
                    const settings = JSON.parse(savedSettings);
                    applyAllSettings(settings);
                    showNotification('📂 تم التحميل السريع بنجاح!', 'success');
                    console.log("📂 Quick load completed");
                } else {
                    showNotification('⚠️ لا توجد إعدادات محفوظة', 'warning');
                }
            } catch (error) {
                showNotification('❌ فشل في التحميل السريع', 'error');
                console.error("Error in quick load:", error);
            }
        });

        // Quick Reset
        document.getElementById('quick-reset')?.addEventListener('click', function() {
            if (confirm('🔄 هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
                try {
                    resetAllSettings();
                    showNotification('🔄 تم إعادة تعيين الإعدادات بنجاح!', 'success');
                    console.log("🔄 Quick reset completed");
                } catch (error) {
                    showNotification('❌ فشل في إعادة التعيين', 'error');
                    console.error("Error in quick reset:", error);
                }
            }
        });

        // Quick Preview
        document.getElementById('quick-preview')?.addEventListener('click', function() {
            try {
                togglePreviewMode();
                console.log("👁️ Preview mode toggled");
            } catch (error) {
                console.error("Error in preview mode:", error);
            }
        });

        console.log("✅ Quick actions initialized");
    } catch (error) {
        console.error("❌ Error initializing quick actions:", error);
    }
}

// Helper Functions
function gatherAllSettings() {
    const settings = {
        colors: {},
        positions: {},
        visibility: {},
        styles: {},
        timestamp: Date.now()
    };

    // Gather colors
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.includes('-color')) {
            settings.colors[key] = localStorage.getItem(key);
        }
        if (key && key.includes('-position')) {
            settings.positions[key] = localStorage.getItem(key);
        }
        if (key && key.includes('-Status')) {
            settings.visibility[key] = localStorage.getItem(key);
        }
    }

    return settings;
}

function applyAllSettings(settings) {
    try {
        // Apply colors
        Object.keys(settings.colors || {}).forEach(key => {
            localStorage.setItem(key, settings.colors[key]);
        });

        // Apply positions
        Object.keys(settings.positions || {}).forEach(key => {
            localStorage.setItem(key, settings.positions[key]);
        });

        // Apply visibility
        Object.keys(settings.visibility || {}).forEach(key => {
            localStorage.setItem(key, settings.visibility[key]);
        });

        // Reload to apply changes
        setTimeout(() => location.reload(), 500);
    } catch (error) {
        console.error("Error applying settings:", error);
    }
}

function resetAllSettings() {
    try {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.includes('-color') || key.includes('-position') || key.includes('-Status'))) {
                keysToRemove.push(key);
            }
        }

        keysToRemove.forEach(key => localStorage.removeItem(key));
        setTimeout(() => location.reload(), 500);
    } catch (error) {
        console.error("Error resetting settings:", error);
    }
}

function togglePreviewMode() {
    const hud = document.getElementById('Hud');
    if (hud) {
        hud.style.opacity = hud.style.opacity === '0.5' ? '1' : '0.5';
        showNotification(hud.style.opacity === '0.5' ? '👁️ وضع المعاينة مفعل' : '👁️ وضع المعاينة معطل', 'info');
    }
}

function showNotification(message, type = 'info') {
    try {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '600',
            fontSize: '14px',
            zIndex: '10000',
            opacity: '0',
            transform: 'translateY(-20px)',
            transition: 'all 0.3s ease'
        });

        // Set background color based on type
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        notification.style.background = colors[type] || colors.info;

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);

    } catch (error) {
        console.error("Error showing notification:", error);
    }
}

// Advanced Color System
function initAdvancedColorSystem() {
    try {
        // Color picker inputs
        const colorInputs = document.querySelectorAll('input[type="color"]');
        const textInputs = document.querySelectorAll('input[type="text"][id$="-color-text"]');

        colorInputs.forEach(input => {
            input.addEventListener('change', function() {
                const targetElement = this.id.replace('-color-picker', '');
                const textInput = document.getElementById(targetElement + '-color-text');
                if (textInput) {
                    textInput.value = this.value;
                }
                applyColorToElement(targetElement, this.value);
                updateColorPreview(targetElement, this.value);
            });
        });

        textInputs.forEach(input => {
            input.addEventListener('change', function() {
                const targetElement = this.id.replace('-color-text', '');
                const colorInput = document.getElementById(targetElement + '-color-picker');
                if (colorInput && isValidColor(this.value)) {
                    colorInput.value = this.value;
                    applyColorToElement(targetElement, this.value);
                    updateColorPreview(targetElement, this.value);
                }
            });
        });

        // Color reset buttons
        document.querySelectorAll('.color-reset-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const target = this.getAttribute('data-target');
                resetElementColor(target);
            });
        });

        // Advanced color tools
        document.getElementById('generate-colors')?.addEventListener('click', generateColorScheme);
        document.getElementById('save-color-preset')?.addEventListener('click', saveColorPreset);
        document.getElementById('export-colors')?.addEventListener('click', exportColors);
        document.getElementById('import-colors')?.addEventListener('click', importColors);

        console.log("✅ Advanced color system initialized");
    } catch (error) {
        console.error("❌ Error initializing advanced color system:", error);
    }
}

function applyColorToElement(element, color) {
    try {
        const elements = document.querySelectorAll(`[class*="${element}"], #${element}`);
        elements.forEach(el => {
            if (element === 'health' || element === 'armor' || element === 'hunger' || element === 'thirst' || element === 'stamina') {
                el.style.stroke = color;
                el.style.fill = color + '20'; // Add transparency
            } else {
                el.style.color = color;
            }
        });

        // Save to localStorage
        localStorage.setItem(`${element}-color`, color);

        console.log(`🎨 Applied color ${color} to ${element}`);
    } catch (error) {
        console.error("Error applying color:", error);
    }
}

function updateColorPreview(element, color) {
    try {
        const preview = document.querySelector(`.${element}-color-preview`);
        if (preview) {
            if (preview.classList.contains('preview-circle')) {
                preview.style.borderColor = color;
                preview.style.background = color + '20';
            } else {
                preview.style.color = color;
            }
        }
    } catch (error) {
        console.error("Error updating color preview:", error);
    }
}

function resetElementColor(element) {
    try {
        const defaultColors = {
            health: '#cc3c2e',
            armor: '#3585da',
            hunger: '#86fa01',
            thirst: '#00b6e3',
            stamina: '#ffc100',
            wallet: '#10b981',
            bank: '#3b82f6'
        };

        const defaultColor = defaultColors[element];
        if (defaultColor) {
            const colorPicker = document.getElementById(`${element}-color-picker`);
            const textInput = document.getElementById(`${element}-color-text`);

            if (colorPicker) colorPicker.value = defaultColor;
            if (textInput) textInput.value = defaultColor;

            applyColorToElement(element, defaultColor);
            updateColorPreview(element, defaultColor);

            showNotification(`🔄 تم إعادة تعيين لون ${element}`, 'success');
        }
    } catch (error) {
        console.error("Error resetting color:", error);
    }
}

function isValidColor(color) {
    const s = new Option().style;
    s.color = color;
    return s.color !== '';
}

function generateColorScheme() {
    try {
        const scheme = document.getElementById('color-scheme-generator').value;
        const baseColor = '#6366f1'; // Primary color

        let colors = [];
        switch (scheme) {
            case 'complementary':
                colors = generateComplementaryColors(baseColor);
                break;
            case 'analogous':
                colors = generateAnalogousColors(baseColor);
                break;
            case 'triadic':
                colors = generateTriadicColors(baseColor);
                break;
            case 'monochromatic':
                colors = generateMonochromaticColors(baseColor);
                break;
        }

        // Apply generated colors
        const elements = ['health', 'armor', 'hunger', 'thirst', 'stamina'];
        elements.forEach((element, index) => {
            if (colors[index]) {
                const colorPicker = document.getElementById(`${element}-color-picker`);
                const textInput = document.getElementById(`${element}-color-text`);

                if (colorPicker) colorPicker.value = colors[index];
                if (textInput) textInput.value = colors[index];

                applyColorToElement(element, colors[index]);
                updateColorPreview(element, colors[index]);
            }
        });

        showNotification('🌈 تم توليد مجموعة ألوان جديدة!', 'success');
    } catch (error) {
        console.error("Error generating color scheme:", error);
        showNotification('❌ فشل في توليد الألوان', 'error');
    }
}

function generateComplementaryColors(baseColor) {
    // Simple complementary color generation
    return [baseColor, '#ff6b35', '#4ecdc4', '#45b7d1', '#f9ca24'];
}

function generateAnalogousColors(baseColor) {
    // Simple analogous color generation
    return [baseColor, '#8b5cf6', '#06d6a0', '#118ab2', '#073b4c'];
}

function generateTriadicColors(baseColor) {
    // Simple triadic color generation
    return [baseColor, '#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24'];
}

function generateMonochromaticColors(baseColor) {
    // Simple monochromatic color generation
    return ['#3730a3', '#4338ca', '#4f46e5', '#6366f1', '#818cf8'];
}

// Advanced Settings System
function initAdvancedSettings() {
    try {
        // Performance settings
        document.getElementById('animations-toggle')?.addEventListener('change', toggleAnimations);
        document.getElementById('blur-effects-toggle')?.addEventListener('change', toggleBlurEffects);
        document.getElementById('glow-effects-toggle')?.addEventListener('change', toggleGlowEffects);
        document.getElementById('update-rate')?.addEventListener('input', updateRefreshRate);

        // Display settings
        document.getElementById('hud-scale')?.addEventListener('input', updateHudScale);
        document.getElementById('dark-mode-toggle')?.addEventListener('change', toggleDarkMode);
        document.getElementById('global-opacity')?.addEventListener('input', updateGlobalOpacity);
        document.getElementById('focus-mode-toggle')?.addEventListener('change', toggleFocusMode);

        // Interaction settings
        document.getElementById('keyboard-shortcuts-toggle')?.addEventListener('change', toggleKeyboardShortcuts);
        document.getElementById('mouse-control-toggle')?.addEventListener('change', toggleMouseControl);
        document.getElementById('sounds-toggle')?.addEventListener('change', toggleSounds);
        document.getElementById('vibration-toggle')?.addEventListener('change', toggleVibration);

        // System settings
        document.getElementById('auto-save-toggle')?.addEventListener('change', toggleAutoSave);
        document.getElementById('auto-update-toggle')?.addEventListener('change', toggleAutoUpdate);
        document.getElementById('analytics-toggle')?.addEventListener('change', toggleAnalytics);

        // Load saved settings
        loadAdvancedSettings();

        console.log("✅ Advanced settings initialized");
    } catch (error) {
        console.error("❌ Error initializing advanced settings:", error);
    }
}

function toggleAnimations(event) {
    const enabled = event.target.checked;
    document.documentElement.style.setProperty('--transition', enabled ? 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)' : 'none');
    localStorage.setItem('animations-enabled', enabled);
    showNotification(enabled ? '🎭 تم تفعيل الانيميشن' : '🎭 تم إيقاف الانيميشن', 'info');
}

function toggleBlurEffects(event) {
    const enabled = event.target.checked;
    document.documentElement.style.setProperty('--blur-bg', enabled ? 'blur(16px)' : 'none');
    localStorage.setItem('blur-effects-enabled', enabled);
    showNotification(enabled ? '🌫️ تم تفعيل تأثيرات التمويه' : '🌫️ تم إيقاف تأثيرات التمويه', 'info');
}

function toggleGlowEffects(event) {
    const enabled = event.target.checked;
    const elements = document.querySelectorAll('.progressplayer');
    elements.forEach(el => {
        if (enabled) {
            el.style.filter = 'drop-shadow(0 0 8px currentColor)';
        } else {
            el.style.filter = 'none';
        }
    });
    localStorage.setItem('glow-effects-enabled', enabled);
    showNotification(enabled ? '✨ تم تفعيل تأثيرات الإضاءة' : '✨ تم إيقاف تأثيرات الإضاءة', 'info');
}

function updateRefreshRate(event) {
    const rate = event.target.value;
    document.querySelector('.range-value').textContent = rate + 'ms';
    localStorage.setItem('update-rate', rate);
    showNotification(`🔄 تم تحديث معدل التحديث إلى ${rate}ms`, 'info');
}

function updateHudScale(event) {
    const scale = event.target.value;
    const percentage = Math.round(scale * 100);
    document.querySelector('#hud-scale + .range-value').textContent = percentage + '%';
    document.getElementById('Hud').style.transform = `scale(${scale})`;
    localStorage.setItem('hud-scale', scale);
    showNotification(`📏 تم تحديث حجم الـ HUD إلى ${percentage}%`, 'info');
}

function updateGlobalOpacity(event) {
    const opacity = event.target.value;
    const percentage = Math.round(opacity * 100);
    document.querySelector('#global-opacity + .range-value').textContent = percentage + '%';
    document.getElementById('Hud').style.opacity = opacity;
    localStorage.setItem('global-opacity', opacity);
    showNotification(`👁️ تم تحديث الشفافية إلى ${percentage}%`, 'info');
}

function loadAdvancedSettings() {
    try {
        // Load and apply saved settings
        const animationsEnabled = localStorage.getItem('animations-enabled') !== 'false';
        const blurEnabled = localStorage.getItem('blur-effects-enabled') !== 'false';
        const glowEnabled = localStorage.getItem('glow-effects-enabled') !== 'false';
        const updateRate = localStorage.getItem('update-rate') || '500';
        const hudScale = localStorage.getItem('hud-scale') || '1';
        const globalOpacity = localStorage.getItem('global-opacity') || '1';

        // Apply settings
        document.getElementById('animations-toggle').checked = animationsEnabled;
        document.getElementById('blur-effects-toggle').checked = blurEnabled;
        document.getElementById('glow-effects-toggle').checked = glowEnabled;
        document.getElementById('update-rate').value = updateRate;
        document.getElementById('hud-scale').value = hudScale;
        document.getElementById('global-opacity').value = globalOpacity;

        // Update displays
        document.querySelector('#update-rate + .range-value').textContent = updateRate + 'ms';
        document.querySelector('#hud-scale + .range-value').textContent = Math.round(hudScale * 100) + '%';
        document.querySelector('#global-opacity + .range-value').textContent = Math.round(globalOpacity * 100) + '%';

        console.log("✅ Advanced settings loaded");
    } catch (error) {
        console.error("Error loading advanced settings:", error);
    }
}

// Presets System
function initPresetsSystem() {
    try {
        // Preset buttons
        document.querySelectorAll('.apply-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const preset = this.getAttribute('data-preset');
                applyPreset(preset);
            });
        });

        document.querySelectorAll('.preview-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const preset = this.getAttribute('data-preset');
                previewPreset(preset);
            });
        });

        // Custom preset creation
        document.getElementById('create-custom-preset')?.addEventListener('click', createCustomPreset);

        // Load saved custom presets
        loadCustomPresets();

        console.log("✅ Presets system initialized");
    } catch (error) {
        console.error("❌ Error initializing presets system:", error);
    }
}

function applyPreset(presetName) {
    try {
        const presets = {
            classic: {
                health: '#cc3c2e',
                armor: '#3585da',
                hunger: '#86fa01',
                thirst: '#00b6e3',
                stamina: '#ffc100',
                wallet: '#10b981',
                bank: '#3b82f6'
            },
            modern: {
                health: '#ef4444',
                armor: '#3b82f6',
                hunger: '#10b981',
                thirst: '#06b6d4',
                stamina: '#f59e0b',
                wallet: '#8b5cf6',
                bank: '#6366f1'
            },
            neon: {
                health: '#ff0080',
                armor: '#00ffff',
                hunger: '#ffff00',
                thirst: '#ff00ff',
                stamina: '#00ff00',
                wallet: '#ff8000',
                bank: '#8000ff'
            },
            minimal: {
                health: '#6b7280',
                armor: '#6b7280',
                hunger: '#6b7280',
                thirst: '#6b7280',
                stamina: '#6b7280',
                wallet: '#6b7280',
                bank: '#6b7280'
            },
            racing: {
                health: '#ff4500',
                armor: '#1e90ff',
                hunger: '#ffd700',
                thirst: '#00ced1',
                stamina: '#ff6347',
                wallet: '#32cd32',
                bank: '#4169e1'
            },
            military: {
                health: '#8b4513',
                armor: '#556b2f',
                hunger: '#2f4f4f',
                thirst: '#708090',
                stamina: '#696969',
                wallet: '#8fbc8f',
                bank: '#5f8a5f'
            },
            fantasy: {
                health: '#dc143c',
                armor: '#4169e1',
                hunger: '#daa520',
                thirst: '#20b2aa',
                stamina: '#9370db',
                wallet: '#ff69b4',
                bank: '#4682b4'
            }
        };

        const preset = presets[presetName];
        if (preset) {
            Object.keys(preset).forEach(element => {
                const color = preset[element];

                // Update color inputs
                const colorPicker = document.getElementById(`${element}-color-picker`);
                const textInput = document.getElementById(`${element}-color-text`);

                if (colorPicker) colorPicker.value = color;
                if (textInput) textInput.value = color;

                // Apply color
                applyColorToElement(element, color);
                updateColorPreview(element, color);
            });

            showNotification(`🎨 تم تطبيق قالب ${getPresetDisplayName(presetName)}`, 'success');
            console.log(`🎨 Applied preset: ${presetName}`);
        }
    } catch (error) {
        console.error("Error applying preset:", error);
        showNotification('❌ فشل في تطبيق القالب', 'error');
    }
}

function previewPreset(presetName) {
    try {
        // Store current colors
        const currentColors = {};
        ['health', 'armor', 'hunger', 'thirst', 'stamina', 'wallet', 'bank'].forEach(element => {
            const colorPicker = document.getElementById(`${element}-color-picker`);
            if (colorPicker) {
                currentColors[element] = colorPicker.value;
            }
        });

        // Apply preset temporarily
        applyPreset(presetName);

        // Revert after 3 seconds
        setTimeout(() => {
            Object.keys(currentColors).forEach(element => {
                const color = currentColors[element];
                const colorPicker = document.getElementById(`${element}-color-picker`);
                const textInput = document.getElementById(`${element}-color-text`);

                if (colorPicker) colorPicker.value = color;
                if (textInput) textInput.value = color;

                applyColorToElement(element, color);
                updateColorPreview(element, color);
            });

            showNotification('👁️ انتهت المعاينة', 'info');
        }, 3000);

        showNotification(`👁️ معاينة قالب ${getPresetDisplayName(presetName)} لمدة 3 ثوان`, 'info');
    } catch (error) {
        console.error("Error previewing preset:", error);
    }
}

function getPresetDisplayName(presetName) {
    const names = {
        classic: 'الكلاسيكي',
        modern: 'الحديث',
        neon: 'النيون',
        minimal: 'المبسط',
        racing: 'السباق',
        military: 'العسكري',
        fantasy: 'الخيال'
    };
    return names[presetName] || presetName;
}

function createCustomPreset() {
    try {
        const name = document.getElementById('custom-preset-name').value.trim();
        const description = document.getElementById('custom-preset-description').value.trim();

        if (!name) {
            showNotification('⚠️ يرجى إدخال اسم للقالب', 'warning');
            return;
        }

        // Gather current colors
        const colors = {};
        ['health', 'armor', 'hunger', 'thirst', 'stamina', 'wallet', 'bank'].forEach(element => {
            const colorPicker = document.getElementById(`${element}-color-picker`);
            if (colorPicker) {
                colors[element] = colorPicker.value;
            }
        });

        // Create preset object
        const preset = {
            name: name,
            description: description || 'قالب مخصص',
            colors: colors,
            created: Date.now()
        };

        // Save to localStorage
        const customPresets = JSON.parse(localStorage.getItem('custom-presets') || '[]');
        customPresets.push(preset);
        localStorage.setItem('custom-presets', JSON.stringify(customPresets));

        // Clear form
        document.getElementById('custom-preset-name').value = '';
        document.getElementById('custom-preset-description').value = '';

        // Reload custom presets display
        loadCustomPresets();

        showNotification(`✅ تم إنشاء القالب "${name}" بنجاح`, 'success');
        console.log(`✅ Created custom preset: ${name}`);
    } catch (error) {
        console.error("Error creating custom preset:", error);
        showNotification('❌ فشل في إنشاء القالب', 'error');
    }
}

function loadCustomPresets() {
    try {
        const container = document.getElementById('saved-presets-container');
        if (!container) return;

        const customPresets = JSON.parse(localStorage.getItem('custom-presets') || '[]');

        if (customPresets.length === 0) {
            container.innerHTML = '<p style="color: var(--text-secondary); text-align: center; padding: 20px;">لا توجد قوالب مخصصة محفوظة</p>';
            return;
        }

        container.innerHTML = customPresets.map((preset, index) => `
            <div class="preset-card custom-preset" data-index="${index}">
                <div class="preset-preview">
                    <div class="preview-elements">
                        <div class="preview-health" style="background: ${preset.colors.health};"></div>
                        <div class="preview-armor" style="background: ${preset.colors.armor};"></div>
                        <div class="preview-money" style="background: ${preset.colors.wallet};">$1,234</div>
                    </div>
                </div>
                <div class="preset-info">
                    <h5>👤 ${preset.name}</h5>
                    <p>${preset.description}</p>
                    <div class="preset-actions">
                        <button class="preset-btn apply-btn" onclick="applyCustomPreset(${index})">تطبيق</button>
                        <button class="preset-btn preview-btn" onclick="previewCustomPreset(${index})">معاينة</button>
                        <button class="preset-btn delete-btn" onclick="deleteCustomPreset(${index})" style="background: #ef4444;">حذف</button>
                    </div>
                </div>
            </div>
        `).join('');

        console.log(`✅ Loaded ${customPresets.length} custom presets`);
    } catch (error) {
        console.error("Error loading custom presets:", error);
    }
}

function applyCustomPreset(index) {
    try {
        const customPresets = JSON.parse(localStorage.getItem('custom-presets') || '[]');
        const preset = customPresets[index];

        if (preset) {
            Object.keys(preset.colors).forEach(element => {
                const color = preset.colors[element];

                const colorPicker = document.getElementById(`${element}-color-picker`);
                const textInput = document.getElementById(`${element}-color-text`);

                if (colorPicker) colorPicker.value = color;
                if (textInput) textInput.value = color;

                applyColorToElement(element, color);
                updateColorPreview(element, color);
            });

            showNotification(`🎨 تم تطبيق القالب "${preset.name}"`, 'success');
        }
    } catch (error) {
        console.error("Error applying custom preset:", error);
    }
}

function previewCustomPreset(index) {
    try {
        const customPresets = JSON.parse(localStorage.getItem('custom-presets') || '[]');
        const preset = customPresets[index];

        if (preset) {
            // Store current colors
            const currentColors = {};
            Object.keys(preset.colors).forEach(element => {
                const colorPicker = document.getElementById(`${element}-color-picker`);
                if (colorPicker) {
                    currentColors[element] = colorPicker.value;
                }
            });

            // Apply preset temporarily
            applyCustomPreset(index);

            // Revert after 3 seconds
            setTimeout(() => {
                Object.keys(currentColors).forEach(element => {
                    const color = currentColors[element];
                    const colorPicker = document.getElementById(`${element}-color-picker`);
                    const textInput = document.getElementById(`${element}-color-text`);

                    if (colorPicker) colorPicker.value = color;
                    if (textInput) textInput.value = color;

                    applyColorToElement(element, color);
                    updateColorPreview(element, color);
                });

                showNotification('👁️ انتهت المعاينة', 'info');
            }, 3000);

            showNotification(`👁️ معاينة القالب "${preset.name}" لمدة 3 ثوان`, 'info');
        }
    } catch (error) {
        console.error("Error previewing custom preset:", error);
    }
}

function deleteCustomPreset(index) {
    try {
        const customPresets = JSON.parse(localStorage.getItem('custom-presets') || '[]');
        const preset = customPresets[index];

        if (preset && confirm(`هل أنت متأكد من حذف القالب "${preset.name}"؟`)) {
            customPresets.splice(index, 1);
            localStorage.setItem('custom-presets', JSON.stringify(customPresets));
            loadCustomPresets();
            showNotification(`🗑️ تم حذف القالب "${preset.name}"`, 'success');
        }
    } catch (error) {
        console.error("Error deleting custom preset:", error);
    }
}

// إصلاح مشكلة عرض الخريطة
function fixMapDisplay() {
    try {
        const mapContainer = document.querySelector('#Hud .outline .mapborder');
        if (mapContainer) {
            // التأكد من أن الخريطة تملأ الحاوية
            const mapContent = mapContainer.children[0];
            if (mapContent) {
                mapContent.style.width = '100%';
                mapContent.style.height = '100%';
                mapContent.style.position = 'absolute';
                mapContent.style.top = '0';
                mapContent.style.left = '0';
                mapContent.style.borderRadius = 'inherit';
            }

            // إصلاح الحدود
            mapContainer.style.overflow = 'hidden';
            mapContainer.style.display = 'flex';
            mapContainer.style.alignItems = 'center';
            mapContainer.style.justifyContent = 'center';

            console.log("✅ Map display fixed");
        }

        // إصلاح تبديل شكل الخريطة
        const mapToggleBtn = document.getElementById('map-toggle-btn');
        if (mapToggleBtn) {
            mapToggleBtn.addEventListener('click', function() {
                const mapContainer = document.querySelector('#Hud .outline .mapborder');
                if (mapContainer) {
                    if (mapContainer.style.borderRadius === '50%' || mapContainer.style.borderRadius === '') {
                        // تحويل إلى مربع
                        mapContainer.style.borderRadius = '15px';
                        mapContainer.classList.remove('circle');
                        mapContainer.classList.add('square');

                        // تطبيق على المحتوى
                        const mapContent = mapContainer.children[0];
                        if (mapContent) {
                            mapContent.style.borderRadius = '15px';
                        }

                        showNotification('🗺️ تم تغيير الخريطة إلى شكل مربع', 'info');
                    } else {
                        // تحويل إلى دائري
                        mapContainer.style.borderRadius = '50%';
                        mapContainer.classList.remove('square');
                        mapContainer.classList.add('circle');

                        // تطبيق على المحتوى
                        const mapContent = mapContainer.children[0];
                        if (mapContent) {
                            mapContent.style.borderRadius = '50%';
                        }

                        showNotification('🗺️ تم تغيير الخريطة إلى شكل دائري', 'info');
                    }

                    // حفظ الإعداد
                    localStorage.setItem('map-shape', mapContainer.classList.contains('square') ? 'square' : 'circle');
                }
            });
        }

        // تطبيق الشكل المحفوظ
        const savedShape = localStorage.getItem('map-shape');
        if (savedShape && mapContainer) {
            if (savedShape === 'square') {
                mapContainer.style.borderRadius = '15px';
                mapContainer.classList.add('square');
                const mapContent = mapContainer.children[0];
                if (mapContent) {
                    mapContent.style.borderRadius = '15px';
                }
            } else {
                mapContainer.style.borderRadius = '50%';
                mapContainer.classList.add('circle');
                const mapContent = mapContainer.children[0];
                if (mapContent) {
                    mapContent.style.borderRadius = '50%';
                }
            }
        }

    } catch (error) {
        console.error("❌ Error fixing map display:", error);
    }
}

// إصلاح مشاكل العرض والأشكال
function fixDisplayIssues() {
    try {
        // إزالة أي خلفيات سوداء
        const blackElements = document.querySelectorAll('[style*="background: black"], [style*="background: #000"], [style*="background-color: black"], [style*="background-color: #000"]');
        blackElements.forEach(el => {
            el.style.background = 'transparent';
            el.style.backgroundColor = 'transparent';
        });

        // إصلاح عناصر القائمة
        const panelElements = document.querySelectorAll('.Panl *');
        panelElements.forEach(el => {
            // إزالة أي خلفيات سوداء
            if (el.style.backgroundColor === 'black' || el.style.backgroundColor === '#000000' || el.style.backgroundColor === 'rgb(0, 0, 0)') {
                el.style.backgroundColor = 'transparent';
            }
            if (el.style.background === 'black' || el.style.background === '#000000' || el.style.background === 'rgb(0, 0, 0)') {
                el.style.background = 'transparent';
            }
        });

        // تحسين عرض البطاقات
        const cards = document.querySelectorAll('.style-card, .element-category, .color-category, .preset-card');
        cards.forEach(card => {
            card.style.background = 'linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.08) 100%)';
            card.style.border = '1px solid rgba(255, 255, 255, 0.15)';
            card.style.backdropFilter = 'blur(8px)';
        });

        // تحسين عرض المعاينات
        const previews = document.querySelectorAll('.style-preview, .color-preview, .preset-preview');
        previews.forEach(preview => {
            preview.style.background = 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.1) 100%)';
            preview.style.border = '1px solid rgba(255, 255, 255, 0.2)';
        });

        // إصلاح مشكلة الحركة
        const panel = document.getElementById('Panl');
        if (panel) {
            panel.style.position = 'fixed';
            panel.style.top = '50%';
            panel.style.left = '50%';
            panel.style.transform = 'translate(-50%, -50%)';
            panel.style.userSelect = 'none';
            panel.style.webkitUserDrag = 'none';

            // منع السحب من الهيدر
            const header = panel.querySelector('.header');
            if (header) {
                header.style.cursor = 'default';
                header.addEventListener('mousedown', function(e) {
                    if (!e.target.closest('button, input, select, .tab-btn, .quick-btn, .options-button, #close')) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                });
            }
        }

        // تحسين الاستجابة
        updateResponsiveLayout();

        console.log("✅ Display issues fixed");
    } catch (error) {
        console.error("❌ Error fixing display issues:", error);
    }
}

// تحديث التخطيط المتجاوب
function updateResponsiveLayout() {
    try {
        const panel = document.getElementById('Panl');
        if (!panel) return;

        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;

        if (screenWidth <= 480) {
            // شاشات صغيرة جداً
            panel.style.width = '100vw';
            panel.style.height = '100vh';
            panel.style.borderRadius = '0';
            panel.style.top = '0';
            panel.style.left = '0';
            panel.style.transform = 'none';
        } else if (screenWidth <= 768) {
            // شاشات صغيرة
            panel.style.width = '98vw';
            panel.style.height = '95vh';
            panel.style.borderRadius = '12px';
            panel.style.top = '50%';
            panel.style.left = '50%';
            panel.style.transform = 'translate(-50%, -50%)';
        } else if (screenWidth <= 1024) {
            // شاشات متوسطة
            panel.style.width = '95vw';
            panel.style.height = '90vh';
            panel.style.maxWidth = '1000px';
            panel.style.maxHeight = '700px';
            panel.style.borderRadius = '16px';
            panel.style.top = '50%';
            panel.style.left = '50%';
            panel.style.transform = 'translate(-50%, -50%)';
        } else {
            // شاشات كبيرة
            panel.style.width = '95vw';
            panel.style.height = '90vh';
            panel.style.maxWidth = '1200px';
            panel.style.maxHeight = '800px';
            panel.style.borderRadius = '20px';
            panel.style.top = '50%';
            panel.style.left = '50%';
            panel.style.transform = 'translate(-50%, -50%)';
        }

        console.log(`📱 Layout updated for ${screenWidth}x${screenHeight}`);
    } catch (error) {
        console.error("Error updating responsive layout:", error);
    }
}

// مراقبة تغيير حجم الشاشة
window.addEventListener('resize', function() {
    setTimeout(updateResponsiveLayout, 100);
});

// إصلاح المشاكل عند تحميل الصفحة
window.addEventListener('load', function() {
    setTimeout(fixDisplayIssues, 500);
});

	</script>
</body>
</html>