# Night HUD - Color Control System

## Overview
The Night HUD now has a comprehensive color control system that allows you to customize the colors of all HUD elements directly from within the game.

## How to Use the Color System

### 1. Opening the Color Panel
- Press `L` key (or your configured key) to open the HUD settings panel
- Or use the command `/hud` if enabled in config
- Or access it through the F1 menu if enabled

### 2. Changing Colors
1. In the settings panel, find the color selection dropdown (تغير لون)
2. Select the HUD element you want to change the color for
3. Use the color picker that appears to choose your desired color
4. The color will be applied immediately and saved automatically

### 3. Available Color Options

#### Health & Status Elements
- **الصحة (Health)** - Health bar color
- **الدرع (Armor)** - Armor bar color  
- **الجوع (Hunger)** - Hunger bar color
- **العطش (Thirst)** - Thirst bar color
- **القدرة على التحمل (Stamina)** - Stamina bar color

#### Player Information
- **الهوية (ID)** - Player ID text color
- **الكاش (Cash)** - Cash amount text color
- **البنك (Bank)** - Bank amount text color
- **اموال قذرة (Dirty Money)** - Dirty money text color
- **الوظيفة (Job)** - Job name text color

#### Vehicle Elements
- **عداد السرعة (Speedometer)** - Speed display color
- **البترول (Fuel)** - Fuel gauge color
- **حزام الأمان (Seatbelt)** - Seatbelt indicator color
- **الأضواء (Lights)** - Vehicle lights indicator color

#### Map & Location
- **الخريطة (Map)** - Map border color
- **اقرب مربع (Nearest Postal)** - Postal code display color

#### Time & Information
- **الوقت (Time)** - Time display color
- **اللاعبين المتصلين (Online Players)** - Online count color

#### Additional Elements
- **التحكم (Control Panel)** - Settings panel border color
- **العلامة المائية (Watermark)** - Watermark text color
- **اختصارات لوحة المفاتيح (Keybinds)** - Keybind display color
- **السلاح (Weapon)** - Weapon display color

### 4. Resetting Colors
- Use the "إعادة تعيين الالوان" (Reset Colors) button to restore all colors to default
- This will remove all custom color settings and reload the page

### 5. Sharing Settings
- Use the "مشاركة الإعدادات" (Share Settings) button to share your color configuration with other players

## Technical Details

### Color Storage
- All color settings are saved in your browser's localStorage
- Colors persist between game sessions
- Each element's color is stored with its type and value

### Color Types
The system supports different color application types:
- **stroke** - For circular progress bars (health, armor, etc.)
- **color** - For text elements
- **info-color** - For information display elements (money, job, etc.)
- **border-color** - For border styling
- **panl** - For panel styling
- **watermark** - For special watermark elements

### Default Colors
Default colors are defined in `Config.lua` under `Night.DefaultColors` and can be customized by server administrators.

## Troubleshooting

### Color Picker Not Working
1. Make sure you have selected an element from the dropdown first
2. Try refreshing the HUD panel (close and reopen)
3. Check browser console for any JavaScript errors

### Colors Not Saving
1. Ensure your browser allows localStorage
2. Try clearing browser cache and reconfiguring
3. Check if you have sufficient browser storage space

### Colors Not Applying
1. Some elements may require specific selectors - this is handled automatically
2. Try using the reset colors button and reconfiguring
3. Some vehicle elements only show when in a vehicle

## Configuration

Server administrators can modify default colors in `Config.lua`:

```lua
Night.DefaultColors = {
    health = "rgb(204, 60, 46)",
    armor = "rgb(0, 142, 255)",
    -- ... other colors
}
```

## Support

If you encounter any issues with the color system:
1. Check the browser console for errors
2. Try the troubleshooting steps above
3. Contact the script developer with specific details about the issue

---

**Note**: The color system has been enhanced with better element detection, improved color application, and more comprehensive options for customizing your HUD experience.
