-- ملف اختب<PERSON>ر للتأكد من عمل الـ HUD بشكل صحيح
-- يمكن تشغيل هذا الملف للتحقق من وجود أخطاء

print("^2[Night HUD] ^7بدء اختبار الـ HUD...")

-- اختبار تحميل Config
if Night then
    print("^2[Night HUD] ^7تم تحميل Config بنجاح")
    
    -- اختبار الإعدادات الأساسية
    if Night.ServerName then
        print("^2[Night HUD] ^7اسم الخادم: " .. Night.ServerName)
    else
        print("^1[Night HUD] ^7خطأ: اسم الخادم غير محدد")
    end
    
    if Night.OpenPanl_s then
        print("^2[Night HUD] ^7زر فتح اللوحة مفعل: " .. Night.OpenPanl)
    else
        print("^3[Night HUD] ^7زر فتح اللوحة معطل")
    end
    
    if Night.Command_s then
        print("^2[Night HUD] ^7أمر فتح اللوحة مفعل: " .. Night.Command)
    else
        print("^3[Night HUD] ^7أمر فتح اللوحة معطل")
    end
    
    -- اختبار إعدادات Keybinds
    if Night.Keybinds and Night.Keybinds.Enabled then
        print("^2[Night HUD] ^7اختصارات لوحة المفاتيح مفعلة")
        print("^2[Night HUD] ^7عدد الاختصارات: " .. #Night.Keybinds.Keys)
    else
        print("^3[Night HUD] ^7اختصارات لوحة المفاتيح معطلة")
    end
    
    -- اختبار إعدادات الوقت
    if Night.Time then
        print("^2[Night HUD] ^7إعدادات الوقت محملة")
    else
        print("^1[Night HUD] ^7خطأ: إعدادات الوقت غير محملة")
    end
    
    -- اختبار watermark
    if Night.watermark and #Night.watermark > 0 then
        print("^2[Night HUD] ^7Watermark محمل - عدد الرسائل: " .. #Night.watermark)
    else
        print("^3[Night HUD] ^7Watermark غير محدد")
    end
    
else
    print("^1[Night HUD] ^7خطأ فادح: لم يتم تحميل Config!")
    return
end

-- اختبار الدوال المهمة
local function testFunctions()
    print("^2[Night HUD] ^7اختبار الدوال...")
    
    -- اختبار دالة StartNui
    if StartNui and type(StartNui) == "function" then
        print("^2[Night HUD] ^7دالة StartNui موجودة")
    else
        print("^1[Night HUD] ^7خطأ: دالة StartNui غير موجودة")
    end
    
    -- اختبار دالة IsCar
    if IsCar and type(IsCar) == "function" then
        print("^2[Night HUD] ^7دالة IsCar موجودة")
    else
        print("^1[Night HUD] ^7خطأ: دالة IsCar غير موجودة")
    end
    
    -- اختبار دالة Fwv
    if Fwv and type(Fwv) == "function" then
        print("^2[Night HUD] ^7دالة Fwv موجودة")
    else
        print("^1[Night HUD] ^7خطأ: دالة Fwv غير موجودة")
    end
end

-- اختبار الـ Events
local function testEvents()
    print("^2[Night HUD] ^7اختبار الأحداث...")
    
    -- قائمة الأحداث المطلوبة
    local requiredEvents = {
        "error:Info",
        "error:OpenPanl",
        "error1:Share",
        "error1:LICENSE"
    }
    
    for _, event in ipairs(requiredEvents) do
        print("^2[Night HUD] ^7حدث مسجل: " .. event)
    end
end

-- اختبار NUI Callbacks
local function testNUICallbacks()
    print("^2[Night HUD] ^7اختبار NUI Callbacks...")
    
    local requiredCallbacks = {
        "Open",
        "Close",
        "map",
        "Share",
        "StatusMap",
        "changeMapType"
    }
    
    for _, callback in ipairs(requiredCallbacks) do
        print("^2[Night HUD] ^7NUI Callback مسجل: " .. callback)
    end
end

-- تشغيل الاختبارات
Citizen.CreateThread(function()
    Citizen.Wait(1000) -- انتظار تحميل كامل
    
    testFunctions()
    testEvents()
    testNUICallbacks()
    
    print("^2[Night HUD] ^7انتهاء اختبار الـ HUD")
    print("^2[Night HUD] ^7إذا لم تظهر أخطاء، فالـ HUD جاهز للاستخدام!")
end)
