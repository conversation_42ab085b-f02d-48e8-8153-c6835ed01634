local Tunnel = module('vrp','lib/Tunnel')
local Proxy = module('vrp','lib/Proxy')
vRPclient = Tunnel.getInterface("vRP","vRP_basic_menu")
vRP = Proxy.getInterface('vRP')

Config = {}

Config.DiscordBotToken = "https://eafe653e46d8dc8808b813154a91b4f3c86f42b800c787c9b7&" -- أضف التوكن الخاص بك هنا
Config.DefaultAvatarUrl = "https://cdn.discordapp.com/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=a0ca111b3f9ec5eafe653e46d8dc8808b813154a91b4f3c86f42b800c787c9b7&"

-- دالة لجلب معرف Discord
function DiscordId(player)
    local dis = "IdNotFound"
    for i = 0, GetNumPlayerIdentifiers(player) - 1 do
        local den = GetPlayerIdentifier(player, i)
        if den and string.sub(den, 1, 8) == "discord:" then
            local discordId = string.sub(den, 9)
            return discordId
        end
    end
    return dis
end

-- دالة لجلب بيانات المستخدم من Discord
function BaseDiscordequest(method, endpoint, jsondata)
    if not Config.DiscordBotToken or Config.DiscordBotToken == "" then
        return {data = nil, code = 500, headers = {}}
    end
    local data = nil
    PerformHttpRequest("https://discord.com/api/v10/" .. endpoint, function(errorCode, resultData, resultHeaders)
        data = {data = resultData, code = errorCode, headers = resultHeaders}
    end, method, #jsondata > 0 and json.encode(jsondata) or "", {["Content-Type"] = "application/json", ["Authorization"] = "Bot " .. Config.DiscordBotToken})
    while data == nil do 
        Citizen.Wait(0) 
    end
    return data
end

-- دالة لجلب الصورة الرمزية
function GetInfo(user)
    local avatar = nil
    local username = "Unknown"
    local chanel = BaseDiscordequest("GET", "users/" .. user, {})
    if chanel.code == 200 and chanel.data then
        local data = json.decode(chanel.data)
        if data then
            if data.avatar then
                if data.avatar:sub(1, 2) == "a_" then
                    avatar = "https://cdn.discordapp.com/avatars/" .. user .. "/" .. data.avatar .. ".gif?size=128"
                else 
                    avatar = "https://cdn.discordapp.com/avatars/" .. user .. "/" .. data.avatar .. ".png?size=128"
                end
            end
            if data.username then
                username = data.username
            end
        else
            avatar = Config.DefaultAvatarUrl
        end
        return {avatar = avatar, username = username}
    else
        return {avatar = Config.DefaultAvatarUrl, username = "Unknown"}
    end
end

local function OpenPanl(player)
    local user_id = vRP.getUserId({player})
    if user_id ~= nil then
        TriggerClientEvent("error:OpenPanl",player)
    end
end

Citizen.CreateThread(function()
    if Night.Command_s then
        RegisterCommand(Night.Command, function (player, args, rawCommand)
            OpenPanl(player)
        end)
    end
end)

onlineJobs = {}
RegisterServerEvent('error:Info')
AddEventHandler('error:Info', function()
    local _source = source
    local playerID = vRP.getUserId({_source})

    if playerID ~= nil then
        local Thirst = vRP.getThirst({playerID}) or 0
        local hunger = vRP.getHunger({playerID}) or 0

        TriggerClientEvent('error:Info', _source, {
            thirst = math.max(0, 100 - Thirst),
            hunger = math.max(0, 100 - hunger),
            hunger1 = math.max(0, 100 - hunger),
            job = "عاطل",
            id = playerID,
            money = vRP.getMoney({playerID}) or 0,
            bankMoney = vRP.getBankMoney({playerID}) or 0,
            blackMoney = vRP.getInventoryItemAmount({playerID, Night.blackMoney}) or 0,
            online = GetNumPlayerIndices()
        })
    end
end)

local function CheckTheBox(player)
    local user_id = vRP.getUserId({player})
    if user_id ~= nil then
        vRP.prompt({player,"رقم المربع","",function (source, post)
            local cfg = json.decode(LoadResourceFile(GetCurrentResourceName(), "File/postlist.json"))
            for _, v in pairs(cfg) do
                if v.code == post then
                    vRPclient.setGPS(source,{v.x,v.y})
                end
            end
        end})
    end
end

RegisterServerEvent('error1:Share')
AddEventHandler('error1:Share', function(data)
    local _source = source
    local user_id = vRP.getUserId({_source})
    if user_id ~= nil then
        vRPclient.getNearestPlayer(_source, { 10 }, function(nearestPlayer)
            if nearestPlayer ~= nil then
                vRP.request({nearestPlayer,"صاحب الأيدي "..user_id.." شاركك الاعدادات هل انت موافق؟", 30, function(_, ok)
                    if ok then
                        TriggerClientEvent("error1:Share", nearestPlayer, data)
                    end
                end})
            else
                vRPclient.notify(_source,{"لايوجد لاعب قريب"})
            end
        end)
    end
end)


vRP.registerMenuBuilder({"main", function(add, data)
    local user_id = vRP.getUserId({data.player})
    if (user_id ~= nil) then
        local choices = {}
        if Night.Gui_s then
            choices[Night.Gui] = {OpenPanl}
        end
        if Night.CheckTheBox_s then
            choices[Night.CheckTheBox] = {CheckTheBox}
        end
        add(choices)
    end
end})


