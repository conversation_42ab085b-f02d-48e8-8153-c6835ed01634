# دليل تثبيت Night HUD - النسخة المحسنة

## المتطلبات:

### 1. متطلبات الخادم:
- FiveM Server
- vRP Framework (الإصدار الحديث)
- MySQL Database

### 2. الملفات المطلوبة:
- جميع ملفات المورد في مجلد `Night-Hud`
- صور الـ HUD في مجلد `File/FrontEnd/img`
- ملف `postlist.json` للمربعات

## خطوات التثبيت:

### الخطوة 1: نسخ الملفات
1. انسخ مجلد `Night-Hud` إلى مجلد `resources` في خادمك
2. تأكد من أن جميع الملفات موجودة في أماكنها الصحيحة

### الخطوة 2: تعديل server.cfg
أضف السطر التالي إلى ملف `server.cfg`:
```
ensure Night-Hud
```

### الخطوة 3: تكوين الإعدادات
1. افتح ملف `Config.lua`
2. عدل الإعدادات حسب احتياجاتك:

```lua
-- اسم الخادم
Night.ServerName = "اسم خادمك هنا"

-- رابط Discord
Night.discord = "discord.gg/your-server"

-- إعدادات فتح اللوحة
Night.OpenPanl = "L" -- الزر المستخدم
Night.Command = "hud" -- الأمر المستخدم

-- Discord Bot Token (اختياري)
Night.DiscordBotToken = "your-bot-token-here"
```

### الخطوة 4: إعداد قاعدة البيانات
تأكد من أن vRP يعمل بشكل صحيح وأن قاعدة البيانات متصلة.

### الخطوة 5: إعادة تشغيل الخادم
1. أعد تشغيل الخادم
2. تحقق من console للتأكد من عدم وجود أخطاء

## الاستخدام:

### فتح لوحة الإعدادات:
- **بالزر**: اضغط `L` (أو الزر المحدد في الإعدادات)
- **بالأمر**: اكتب `/hud` في الشات
- **من القائمة**: F1 > اعدادات العرض

### الميزات المتاحة:
1. **عرض المعلومات الأساسية**: الصحة، الدرع، الجوع، العطش
2. **معلومات المركبة**: السرعة، الوقود، حزام الأمان
3. **معلومات اللاعب**: المال، البنك، الوظيفة، الهوية
4. **الخريطة**: عرض دائري أو مربع
5. **الأسلحة**: عرض السلاح الحالي والذخيرة
6. **المربعات**: عرض أقرب مربع
7. **اختصارات لوحة المفاتيح**: عرض الأزرار المهمة

### تخصيص الـ HUD:
1. افتح لوحة الإعدادات
2. اسحب العناصر لتغيير مواقعها
3. غير الألوان حسب رغبتك
4. احفظ الإعدادات أو شاركها مع لاعبين آخرين

## استكشاف الأخطاء:

### مشاكل شائعة وحلولها:

#### 1. الـ HUD لا يظهر:
- تحقق من أن المورد يعمل: `/refresh` ثم `/ensure Night-Hud`
- تحقق من console للأخطاء
- تأكد من أن vRP يعمل بشكل صحيح

#### 2. لا يمكن فتح لوحة الإعدادات:
- تحقق من إعدادات الأزرار في `Config.lua`
- تأكد من أن الأمر `/hud` يعمل
- جرب فتح اللوحة من قائمة F1

#### 3. البيانات لا تتحديث:
- تحقق من اتصال قاعدة البيانات
- تأكد من أن vRP يعمل بشكل صحيح
- راجع console للأخطاء

#### 4. مشاكل في الأداء:
- تحقق من إعدادات الخادم
- قلل من تكرار التحديثات في الكود إذا لزم الأمر

### رسائل الأخطاء الشائعة:

#### `Night is undefined`:
- تأكد من تحميل `Config.lua` قبل `ClientSide.lua`
- تحقق من ترتيب الملفات في `fxmanifest.lua`

#### `vRP function not found`:
- تأكد من أن vRP مثبت ويعمل
- تحقق من إصدار vRP المستخدم

#### `NUI callback error`:
- تحقق من ملف `index.html`
- تأكد من وجود جميع ملفات الصور

## الدعم الفني:

إذا واجهت مشاكل:
1. تحقق من console للأخطاء
2. راجع هذا الدليل
3. تأكد من أن جميع المتطلبات مثبتة
4. اتصل بالدعم الفني

## ملاحظات مهمة:

- هذه النسخة محسنة ومُصلحة من النسخة الأصلية
- تم إصلاح جميع المشاكل المعروفة
- الأداء محسن بشكل كبير
- متوافق مع أحدث إصدارات FiveM و vRP

## التحديثات:

للحصول على التحديثات:
1. تابع صفحة المطور
2. احتفظ بنسخة احتياطية من إعداداتك
3. استبدل الملفات القديمة بالجديدة
4. راجع ملف التغييرات للتحديثات الجديدة
