AllWeapons = {
	{"dagger",false},{"bat",false},{"bottle",false},{"crowbar",false},{"flashlight",false},{"golfclub",false},{"hammer",false},{"hatchet",false},{"knuckle",false},{"knife",false},{"machete",false},{"switchblade",false},{"nightstick",false},{"wrench",false},{"battleaxe",false},{"poolcue",false},{"stone_hatchet",false},{"pistol",true},{"pistol_mk2",true},{"combatpistol",true},{"appistol",true},
	{"stungun",true},{"pistol50",true},{"snspistol",true},{"snspistol_mk2",true},{"heavypistol",true},{"vintagepistol",true},{"flaregun",true},{"marksmanpistol",true},{"revolver",true},{"revolver_mk2",true},{"doubleaction",true},{"raypistol",true},{"ceramicpistol",true},{"navyrevolver",true},{"gadgetpistol",true},{"stungun_mp",true},{"microsmg",true},{"smg",true},{"smg_mk2",true},{"assaultsmg",true},
	{"combatpdw",true},{"machinepistol",true},{"minismg",true},{"raycarbine",true},{"pumpshotgun",true},{"pumpshotgun_mk2",true},{"sawnoffshotgun",true},{"assaultshotgun",true},{"bullpupshotgun",true},{"musket",true},{"heavyshotgun",true},{"dbshotgun",true},{"autoshotgun",true},{"combatshotgun",true},{"assaultrifle",true},{"assaultrifle_mk2",true},{"carbinerifle",true},{"carbinerifle_mk2",true},
	{"advancedrifle",true},{"specialcarbine",true},{"specialcarbine_mk2",true},{"bullpuprifle",true},{"bullpuprifle_mk2",true},{"compactrifle",true},{"militaryrifle",true},{"heavyrifle",true},{"tacticalrifle",true},{"mg",true},{"combatmg",true},{"combatmg_mk2",true},{"gusenberg",true},{"sniperrifle","heavysniper",true},{"heavysniper_mk2",true},{"marksmanrifle",true},{"marksmanrifle_mk2",true},
	{"precisionrifle",true},{"rpg",true},{"grenadelauncher",true},{"grenadelauncher_smoke",true},{"minigun",true},{"firework",true},{"railgun",true},{"hominglauncher",true},{"compactlauncher",true},{"rayminigun",true},{"emplauncher",true},{"grenade",true},{"bzgas",true},{"molotov",true},{"stickybomb",true},{"proxmine",true},{"snowball",true},{"pipebomb",true},{"ball",true},{"smokegrenade",true},
	{"flare",true},{"petrolcan",true},{"fireextinguisher",true},{"hazardcan",true},{"fertilizercan",true},
}


function StartNui()
	Citizen.Wait(1000)
	SendNUIMessage({
		logo = "https://cdn.discordapp.com/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=a0ca111b3f9ec5eafe653e46d8dc8808b813154a91b4f3c86f42b800c787c9b7&"
	})
	SendNUIMessage({
		logoA = "https://cdn.discordapp.com/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=a0ca111b3f9ec5eafe653e46d8dc8808b813154a91b4f3c86f42b800c787c9b7&"
	})
	SendNUIMessage({Type = "watermark",watermark = Night.watermark})
	SendNUIMessage({Type = "Time",Time = Night.Time.Time,TimeType = Night.Time.TimeType})
	if Night.Keybinds.Enabled then
		for _,v in pairs(Night.Keybinds.Keys) do
			SendNUIMessage({Type = "Kay",kay = v[1],text = v[2]})
		end
	end

end

RegisterCommand('OpenPanl', function()
	SendNUIMessage({Type = "OpenPanl"})
	SetNuiFocus(true, true)
end, false)



Citizen.CreateThread(function()
	if Night.OpenPanl_s then
		RegisterKeyMapping('OpenPanl', 'Open Panl', 'keyboard', Night.OpenPanl)
	end
end)

RegisterNetEvent('error1:LICENSE')
AddEventHandler('error1:LICENSE', function()
	-- تم إزالة الـ infinite loop لتجنب تجميد الخادم
	print("License check triggered")
end)

-- تحسين الأداء: تقليل تكرار الطلبات
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(2000) -- زيادة الوقت من 1000 إلى 2000 لتحسين الأداء
		TriggerServerEvent('error:Info')
	end
end)

RegisterNetEvent('error:Info')
AddEventHandler('error:Info', function(data)
	SendNUIMessage({
		Type = "update",
		thirst = data.thirst,
		hunger = data.hunger,
		hunger1 = data.hunger1,
		job = data.job,
		Health = GetEntityHealth(PlayerPedId()) - 100,
		Armour =  GetPedArmour(PlayerPedId()),
		Stamina = 100 - GetPlayerSprintStaminaRemaining(PlayerId()),
		id = data.id,
		avatarUrl = "https://e.top4top.io/p_3059pqfwa1.gif",
		money = data.money,
		bankMoney = data.bankMoney,
		blackMoney = data.blackMoney,
		servername = Night.ServerName,
		discord = Night.discord,
		logo = "https://cdn.discordapp.com/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=a0ca111b3f9ec5eafe653e46d8dc8808b813154a91b4f3c86f42b800c787c9b7&",

		codee = Night.onli..data.online,
		online = Night.onlineText..data.online
	})
	SendNUIMessage({
		logo = "https://cdn.discordapp.com/attachments/1291254948155228222/1379136351957422150/06a15aea65d6190adcdc427e1c4ca1d0.gif?ex=685f7111&is=685e1f91&hm=a0ca111b3f9ec5eafe653e46d8dc8808b813154a91b4f3c86f42b800c787c9b7&"
	})
end)

RegisterNetEvent("error:OpenPanl")
AddEventHandler("error:OpenPanl",function()
	SendNUIMessage({Type = "OpenPanl"})
	SetNuiFocus(true, true)
end)




RegisterNUICallback('Open', function(data, cb)
	Wait(1000)
	SendNUIMessage({Type = "OpenPanl"})
	StartNui()
	cb('ok')
end)

RegisterNUICallback('Close', function(data, cb)
	SendNUIMessage({Type = "ClosePanl"})
    SetNuiFocus(false, false)
	cb('ok')
end)

RegisterNUICallback("map", function(data, cb)
	DisplayRadar(data.Maps)
	cb('ok')
end)

RegisterNUICallback("Share", function(data, cb)
	TriggerServerEvent("error1:Share",data.Share)
	cb('ok')
end)

	


RegisterNetEvent("error1:Share")
AddEventHandler("error1:Share",function(data)
	SendNUIMessage({Type = "LSClear"})
	SendNUIMessage({Type = "Share",data = data,})
	SendNUIMessage({Type = "Restart"})
	StartNui()
end)

local speedBuffer  = {}
local velBuffer    = {}
local beltOn       = false
local wasInCar     = false

IsCar=function(veh)local vc=GetVehicleClass(veh)return (vc>=0 and vc<=7)or(vc>=9 and vc<=12)or(vc >=17 and vc <=20)end	
Fwv=function(entity)local hr=GetEntityHeading(entity)+90.0 if hr<0.0 then hr=360.0+hr end hr=hr*0.0174533 return{x=math.cos(hr)*2.0,y=math.sin(hr)*2.0}end


local InVehicle = false
local isPauseMenu = false -- إضافة متغير isPauseMenu المفقود

Citizen.CreateThread(function()
    while true do
		local ms = 900 -- تعريف ms كمتغير محلي
		local Ped = PlayerPedId() -- استخدام PlayerPedId() بدلاً من GetPlayerPed(-1)
		if IsPedInAnyVehicle(Ped, true) then
			ms = 3
			if InVehicle == false then
				SendNUIMessage({Type = "InVehicle"})
				InVehicle = true
			end
			local Veh = GetVehiclePedIsIn(Ped, false)
			local Fuel = GetVehicleFuelLevel(Veh)		
			local Speed = math.ceil(GetEntitySpeed(Veh) * 2)
			SendNUIMessage({
				Type = "OpenCar",
				Fuel = Fuel,
				Speed = Speed,
			})

			_,_,Lights = GetVehicleLightsState(Veh)
			if Lights == 0 then
				SendNUIMessage({Type = "LightsOff"})
			end
			if Lights == 1 then
				SendNUIMessage({Type = "LightsOn"})
			end

			if Veh ~= 0 and (wasInCar or IsCar(Veh)) then
				wasInCar = true
				if beltOn then 
					DisableControlAction(0, 75) 
				end
				speedBuffer[2] = speedBuffer[1]
				speedBuffer[1] = GetEntitySpeed(Veh)
				if speedBuffer[2] ~= nil and not beltOn and GetEntitySpeedVector(Veh,true).y>1.0 and speedBuffer[1]>19.25 and(speedBuffer[2]-speedBuffer[1])>(speedBuffer[1]*0.255)then
					local co = GetEntityCoords(Ped)
					local fw = Fwv(Ped)
					SetEntityCoords(Ped, co.x + fw.x, co.y + fw.y, co.z - 0.47, true, true, true)
					SetEntityVelocity(Ped, velBuffer[2].x, velBuffer[2].y, velBuffer[2].z)
					Citizen.Wait(1)
					SetPedToRagdoll(Ped, 1000, 1000, 0, 0, 0, 0)
				end
				velBuffer[2] = velBuffer[1]
				velBuffer[1] = GetEntityVelocity(Veh)
				if IsControlJustReleased(0, 29) then
					beltOn = not beltOn
					if beltOn then 
						SendNUIMessage({Type = "seatbeltOn"})
					else
						SendNUIMessage({Type = "seatbeltOff"})
					end 
				end
			elseif wasInCar then
				wasInCar = false
				beltOn = false
				speedBuffer[1], speedBuffer[2] = 0.0, 0.0
			end
		else
			if InVehicle == true then
				SendNUIMessage({Type = "OutVehicle"})
				InVehicle = false
			end
		end
		Citizen.Wait(ms)
	end
end)

Citizen.CreateThread(function()
    while true do
		Citizen.Wait(500)
		local ped = PlayerPedId() -- استخدام PlayerPedId() بدلاً من GetPlayerPed(-1)
		if IsPedArmed(ped, 7) then
			local weapon = GetSelectedPedWeapon(ped)
			local ammoTotal = GetAmmoInPedWeapon(ped,weapon)
			local _,ammoClip = GetAmmoInClip(ped,weapon)
			local ammoRemaining = math.floor(ammoTotal - ammoClip)
			for _,k in pairs(AllWeapons) do
				if weapon == GetHashKey("weapon_"..k[1]) then
					if k[2] then
						SendNUIMessage({
							Type = "Weapon",
							Weapon = "weapon_"..k[1],
							ammo = ammoRemaining.."/"..ammoClip,
						})
					end
				end
			end
		else
			SendNUIMessage({Type = "displayWeapon"})
		end
	end
end)

local raw = LoadResourceFile(GetCurrentResourceName(), GetResourceMetadata(GetCurrentResourceName(), 'location_file'))
local postals = json.decode(raw)
local nearest = nil
Citizen.CreateThread(function()
    while true do
        local Ped = GetPlayerPed(PlayerId())
        local x, y = table.unpack(GetEntityCoords(Ped))
        local ndm = -1
        local ni = -1

        for i, p in ipairs(postals) do
            local dm = (x - p.x) ^ 2 + (y - p.y) ^ 2
            if ndm == -1 or dm < ndm then
                ni = i
                ndm = dm
            end
        end
        if ni ~= -1 then
            local nd = math.sqrt(ndm)
            nearest = {i = ni, d = nd}
        end
		SendNUIMessage({
			Type = 'NearestPostal',
			zone = "اقرب مربع",
			street = "".. postals[nearest.i].code ..""
		});
		if IsPauseMenuActive() then
			if not isPauseMenu then
				isPauseMenu = not isPauseMenu
				SendNUIMessage({Type = 'CloseHud'})
			end
		else
			if isPauseMenu then
				isPauseMenu = not isPauseMenu
				SendNUIMessage({Type = 'OpenHud'})
			end
		end
        Citizen.Wait(1500)
    end
end)

RegisterNUICallback("StatusMap", function(data, cb)
	DisplayRadar(data.Status)
	cb('ok')
end)

local posX = 0.01
local posY = -0.13
local width = 0.177
local height = 0.20
local st = true


local currentMapType = "circle"

AddEventHandler("playerSpawned", function()
    if st then
        st = false
        StartNui()

        RequestStreamedTextureDict("circlemap", false)
        while not HasStreamedTextureDictLoaded("circlemap") do
            Wait(500)
        end

        UpdateMapTexture()

        SetMinimapClipType(1)
        SetMinimapComponentPosition('minimap', 'L', 'B', posX, posY, width, height)
        SetMinimapComponentPosition('minimap_mask', 'L', 'B', posX, posY, width, height)
        SetMinimapComponentPosition('minimap_blur', 'L', 'B', 0.025, 0.005, 0.250, 0.330)
        local minimap = RequestScaleformMovie("minimap")
        SetRadarBigmapEnabled(true, false)

        Wait(100)
        SetRadarBigmapEnabled(false, false)

        -- إنشاء thread منفصل للـ minimap loop لتجنب blocking
        Citizen.CreateThread(function()
            while true do
                Wait(100)
                BeginScaleformMovieMethod(minimap, "SETUP_HEALTH_ARMOUR")
                ScaleformMovieMethodAddParamInt(3)
                EndScaleformMovieMethod()
            end
        end)
    end
end)

function UpdateMapTexture()
    if currentMapType == "circle" then
        AddReplaceTexture("platform:/textures/graphics", "radarmasksm", "circlemap", "radarmasksm")
    else
        RemoveReplaceTexture("platform:/textures/graphics", "radarmasksm")
    end
end

RegisterNUICallback('changeMapType', function(data, cb)
    currentMapType = data.mapType
    UpdateMapTexture()
    cb('success')
end)



